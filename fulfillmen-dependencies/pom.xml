<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.fulfillmen.starter</groupId>
    <version>1.2.7-SNAPSHOT</version>
    <packaging>pom</packaging>
    <artifactId>fulfillmen-dependencies</artifactId>
    <name>Fulfillmen Dependencies ${project.version}</name>
    <description>
        Fulfillmen Dependencies for Fulfillmen System 依赖模块
    </description>

    <!-- 开发人员 -->
    <developers>
        <developer>
            <id>james</id>
            <name>james</name>
            <email><EMAIL></email>
            <roles>
                <role>Creator</role>
                <role>Java Development Engineer</role>
            </roles>
            <timezone>+8</timezone>
        </developer>
    </developers>

    <properties>
        <!-- SpringBoot 版本号 -->
        <spring-boot-starter.revision>3.3.11</spring-boot-starter.revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>17</java.version>
        <!-- 强制覆盖外部settings.xml配置 -->
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>

        <resource.delimiter>@</resource.delimiter>
        <maven.javadoc.failOnError>false</maven.javadoc.failOnError>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 分布式任务调度平台 -->
        <snail-job.version>1.4.0</snail-job.version>
        <!-- 工具依赖版本号 -->
        <hutool.version>5.8.37</hutool.version>
        <!-- IP -->
        <ip2region.version>3.3.6</ip2region.version>
        <!-- 文档 -->
        <knife4j-openapi3.version>4.5.0</knife4j-openapi3.version>
        <!-- CosId（通用、灵活、高性能的分布式 ID 生成器 -->
        <cosid.version>2.12.5</cosid.version>
        <!-- SMS4J（短信聚合框架，轻松集成多家短信服务，解决接入多个短信 SDK 的繁琐流程） -->
        <sms4j.version>3.3.4</sms4j.version>
        <!-- 验证码 -->
        <aj-captcha.version>1.4.0</aj-captcha.version>
        <easy-captcha.version>1.6.2</easy-captcha.version>
        <!-- excel 工具 -->
        <easy-excel.version>3.3.4</easy-excel.version>
        <nashorn.version>15.4</nashorn.version>
        <x-file-storage.version>2.2.1</x-file-storage.version>
        <aws-s3.version>1.12.782</aws-s3.version>
        <s3.version>2.30.35</s3.version>
        <s3-crt.version>0.36.1</s3-crt.version>
        <graceful-response.version>5.0.0-boot3</graceful-response.version>
        <crane4j.version>2.9.0</crane4j.version>
        <knife4j.version>4.5.0</knife4j.version>
        <!-- 跨线程日志埋点 -->
        <tlog.version>1.5.2</tlog.version>
        <ttl.version>2.14.5</ttl.version>
        <!-- graceful-response.version -->
        <graceful-response.version>5.0.5-boot3</graceful-response.version>
        <!-- 认证模块 -->
        <sa-token.version>1.44.0</sa-token.version>
        <just-auth.version>1.16.7</just-auth.version>
        <!-- 数据模块 -->
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <mybatis-flex.version>1.10.9</mybatis-flex.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <p6spy.version>3.9.1</p6spy.version>
        <!-- yaml json 解析 -->
        <snakeyaml.version>2.4</snakeyaml.version>
        <!-- okhttp 请求 -->
        <okhttp.version>4.12.0</okhttp.version>
        <!-- 缓存模块 -->
        <jetcache.version>2.7.8</jetcache.version>
        <redisson.version>3.45.1</redisson.version>
        <!-- Maven Plugin Versions -->
        <flatten.version>1.7.0</flatten.version>
        <spotless.version>2.43.0</spotless.version>
        <sonar.version>3.11.0.3922</sonar.version>
        <xml-maven-plugin.version>1.1.0</xml-maven-plugin.version>
        <build-helper-maven-plugin.version>3.5.0</build-helper-maven-plugin.version>
        <maven-source-plugin.version>3.3.0</maven-source-plugin.version>
        <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
        <maven-javadoc-plugin.version>3.6.3</maven-javadoc-plugin.version>
        <maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
        <lombok-maven-plugin.version>1.18.20.0</lombok-maven-plugin.version>
        <testcontainers.version>1.19.3</testcontainers.version>
        <!-- 解决部分传递依赖漏洞问题 -->
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-io.version>2.17.0</commons-io.version>
        <commons-compress.version>1.26.0</commons-compress.version>
        <!-- 图片缩略 -->
        <thumbnails.version>0.4.20</thumbnails.version>
        <!-- Guava -->
        <guava.version>33.4.8-jre</guava.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-starter.revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>${mybatis-plus.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <!-- Sa-Token（轻量级 Java 权限认证框架，让鉴权变得简单、优雅） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-bom</artifactId>
                <version>${sa-token.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Just Auth（开箱即用的整合第三方登录的开源组件，脱离繁琐的第三方登录 SDK，让登录变得 So easy!） -->
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${just-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xkcoding.justauth</groupId>
                <artifactId>justauth-spring-boot-starter</artifactId>
                <version>1.4.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>me.zhyd.oauth</groupId>
                        <artifactId>JustAuth</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- SnailJob（灵活，可靠和快速的分布式任务重试和分布式任务调度平台） -->
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-starter</artifactId>
                <version>${snail-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-retry-core</artifactId>
                <version>${snail-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-job-core</artifactId>
                <version>${snail-job.version}</version>
            </dependency>

            <!-- MyBatis Flex（MyBatis 的增强工具，在 MyBatis 的基础上只做增强不做改变，简化开发、提高效率） -->
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-dependencies</artifactId>
                <version>${mybatis-flex.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 第三方封装 Ip2region（离线 IP 数据管理框架和定位库，支持亿级别的数据段，10 微秒级别的查询性能，提供了许多主流编程语言的 xdb 数据管理引擎的实现） -->
            <dependency>
                <groupId>net.dreamlu</groupId>
                <artifactId>mica-ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <!-- Hutool（小而全的 Java 工具类库，通过静态方法封装，降低相关 API 的学习成本，提高工作效率，使 Java 拥有函数式语言般的优雅，让 Java 语言也可以"甜甜的"） -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- Knife4j（前身是 swagger-bootstrap-ui，集 Swagger2 和 OpenAPI3 为一体的增强解决方案） -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j-openapi3.version}</version>
            </dependency>

            <!-- TLog（轻量级的分布式日志标记追踪神器） -->
            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>tlog-web-spring-boot-starter</artifactId>
                <version>${tlog.version}</version>
            </dependency>

            <!-- Graceful Response（一个Spring Boot技术栈下的优雅响应处理组件，可以帮助开发者完成响应数据封装、异常处理、错误码填充等过程，提高开发效率，提高代码质量） -->
            <dependency>
                <groupId>com.feiniaojin</groupId>
                <artifactId>graceful-response</artifactId>
                <version>${graceful-response.version}</version>
            </dependency>

            <!-- Dynamic Datasource（基于 Spring Boot 的快速集成多数据源的启动器） -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <!-- P6Spy（SQL 性能分析组件） -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <!-- JetCache（基于 Java 的缓存系统封装，提供统一的 API 和注解来简化缓存的使用。提供了比 SpringCache 更加强大的注解，可以原生的支持 TTL、两级缓存、分布式自动刷新，还提供了 Cache 接口用于手工缓存操作） -->
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-bom</artifactId>
                <version>${jetcache.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Redisson（不仅仅是一个 Redis Java 客户端） -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- CosId（通用、灵活、高性能的分布式 ID 生成器） -->
            <dependency>
                <groupId>me.ahoo.cosid</groupId>
                <artifactId>cosid-bom</artifactId>
                <version>${cosid.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SMS4J（短信聚合框架，轻松集成多家短信服务，解决接入多个短信 SDK 的繁琐流程） -->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>

            <!-- AJ-Captcha（行为验证码，包含滑动拼图、文字点选两种方式，UI支持弹出和嵌入两种方式） -->
            <dependency>
                <groupId>com.anji-plus</groupId>
                <artifactId>captcha</artifactId>
                <version>${aj-captcha.version}</version>
            </dependency>

            <!-- Easy Captcha（Java 图形验证码，支持 gif、中文、算术等类型，可用于 Java Web、JavaSE 等项目） -->
            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>${easy-captcha.version}</version>
            </dependency>

            <!-- JS 引擎（一个纯编译的 JavaScript 引擎） -->
            <dependency>
                <groupId>org.openjdk.nashorn</groupId>
                <artifactId>nashorn-core</artifactId>
                <version>${nashorn.version}</version>
            </dependency>

            <!-- Easy Excel（一个基于 Java 的、快速、简洁、解决大文件内存溢出的 Excel 处理工具） -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easy-excel.version}</version>
            </dependency>

            <!-- Crane4j（一个基于注解的，用于完成一切 "根据 A 的 key 值拿到 B，再把 B 的属性映射到 A" 这类需求的字段填充框架） -->
            <dependency>
                <groupId>cn.crane4j</groupId>
                <artifactId>crane4j-spring-boot-starter</artifactId>
                <version>${crane4j.version}</version>
            </dependency>

            <!-- Knife4j（前身是 swagger-bootstrap-ui，集 Swagger2 和 OpenAPI3 为一体的增强解决方案） -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-dependencies</artifactId>
                <version>${knife4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SnakeYAML（适用于 Java 的 YAML 1.1 解析器和发射器） -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <!-- OkHTTP（一个默认高效的 HTTP 客户端） -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <!-- TTL（线程间传递 ThreadLocal，异步执行时上下文传递的解决方案） -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${ttl.version}</version>
            </dependency>

            <!-- Test Dependencies -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>redis</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <!--图片处理工具-主要用做图片缩略处理-->
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbnails.version}</version>
            </dependency>

            <!-- X File Storage（一行代码将文件存储到本地、FTP、SFTP、WebDAV、阿里云 OSS、华为云 OBS...等其它兼容 S3 协议的存储平台） -->
            <dependency>
                <groupId>org.dromara.x-file-storage</groupId>
                <artifactId>x-file-storage-spring</artifactId>
                <version>${x-file-storage.version}</version>
            </dependency>

            <!-- Amazon S3（Amazon Simple Storage Service，亚马逊简单存储服务，通用存储协议 S3，兼容主流云厂商对象存储） -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-s3.version}</version>
            </dependency>

            <!--  S3  for Java 2.x  -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${s3.version}</version>
            </dependency>

            <!-- 使用 AWS 基于 CRT 的 S3 客户端 -->
            <dependency>
                <groupId>software.amazon.awssdk.crt</groupId>
                <artifactId>aws-crt</artifactId>
                <version>${s3-crt.version}</version>
            </dependency>

            <!-- 基于 AWS CRT 的 S3 客户端的性能增强的 S3 传输管理器 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3-transfer-manager</artifactId>
                <version>${s3.version}</version>
            </dependency>

            <!-- AWS SDK HTTP客户端实现 - Netty (用于异步客户端) -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>netty-nio-client</artifactId>
                <version>${s3.version}</version>
            </dependency>

            <!-- AWS SDK HTTP客户端实现 - Apache (用于同步客户端) -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>apache-client</artifactId>
                <version>${s3.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <!-- Guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- 编译插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                   <version>${maven-compiler-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok-maven-plugin</artifactId>
                    <version>${lombok-maven-plugin.version}</version>
                </plugin>
                <!-- 代码格式化插件 -->
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>${spotless.version}</version>
                </plugin>
                <!-- Sonar 代码质量分析插件 -->
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar.version}</version>
                </plugin>
                <!-- Source Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                </plugin>

                <!-- Deploy Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>

                <!-- Javadoc Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>


    <!-- 依赖仓库配置 -->
    <repositories>
        <!--<repository>
            <id>huawei-mirror</id>
            <name>HuaweiCloud Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>-->
        <repository>
            <id>ali-mirror</id>
            <name>AliYun Mirror</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </repository>
        <repository>
            <id>spring</id>
            <url>https://maven.aliyun.com/repository/spring</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!-- 插件仓库配置 -->
    <pluginRepositories>
        <!--<pluginRepository>
            <id>huawei-mirror</id>
            <name>HuaweiCloud Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </pluginRepository>-->
        <pluginRepository>
            <id>ali-mirror</id>
            <name>AliYun Mirror</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </pluginRepository>
    </pluginRepositories>


</project>
