<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fulfillmen.shop</groupId>
    <artifactId>fulfillmen-shop</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <artifactId>fulfillmen-shop-common</artifactId>
  <name>Fulfillmen Shop Common ${project.version}</name>
  <packaging>jar</packaging>
  <description>公共模块（存放公共工具类，公共配置等）</description>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <!-- 核心依赖模块 -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-core</artifactId>
    </dependency>
    <!-- 缓存 -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-cache-jetcache</artifactId>
    </dependency>
    <!-- Redisson 缓存 - 用于 RedisUtils 工具类 -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-cache-redisson</artifactId>
    </dependency>
    <!-- 使用 kryo5 序列化 -->
    <dependency>
      <groupId>com.esotericsoftware.kryo</groupId>
      <artifactId>kryo5</artifactId>
    </dependency>
    <!-- data core -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-data-core</artifactId>
      <optional>true</optional>
    </dependency>
    <!-- mybatisPlus -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-data-mp</artifactId>
      <optional>true</optional>
    </dependency>
    <!-- CosId（通用、灵活、高性能的分布式 ID 生成器） -->
    <dependency>
      <groupId>me.ahoo.cosid</groupId>
      <artifactId>cosid-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>me.ahoo.cosid</groupId>
      <artifactId>cosid-spring-redis</artifactId>
    </dependency>

    <!-- X File Storage（一行代码将文件存储到本地、FTP、SFTP、WebDAV、阿里云 OSS、华为云 OBS...等其它兼容 S3 协议的存储平台） -->
    <dependency>
      <groupId>org.dromara.x-file-storage</groupId>
      <artifactId>x-file-storage-spring</artifactId>
    </dependency>

    <!-- Amazon S3（Amazon Simple Storage Service，亚马逊简单存储服务，通用存储协议 S3，兼容主流云厂商对象存储） -->
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
    </dependency>

    <!-- SMS4J（短信聚合框架，轻松集成多家短信服务，解决接入多个短信 SDK 的繁琐流程） -->
    <dependency>
      <groupId>org.dromara.sms4j</groupId>
      <artifactId>sms4j-spring-boot-starter</artifactId>
    </dependency>

    <!-- MySQL Java 驱动 -->
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>

    <!-- 认证模块 - SaToken -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-auth-satoken</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-web</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 认证模块 - JustAuth -->
    <!-- 有冲突 redis 配置问题。应该是 autoConfigure 问题 -->
    <!--<dependency>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-auth-justauth</artifactId>
    </dependency>-->

    <!-- 验证码 -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-captcha-graphic</artifactId>
    </dependency>
    <!-- 行为码 -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-captcha-behavior</artifactId>
    </dependency>

    <!-- Liquibase（用于管理数据库版本，跟踪、管理和应用数据库变化） -->
    <dependency>
      <groupId>org.liquibase</groupId>
      <artifactId>liquibase-core</artifactId>
    </dependency>

    <!-- Json 模块 Jackson -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-json-jackson</artifactId>
    </dependency>
    <!-- model 仅编译使用 -->
    <dependency>
      <groupId>com.fulfillmen.shop</groupId>
      <artifactId>fulfillmen-shop-domain</artifactId>
      <scope>provided</scope>
    </dependency>
    <!-- 加密 -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-security-crypto</artifactId>
    </dependency>
    <!-- 密码 -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-security-password</artifactId>
    </dependency>
    <!-- 邮箱 -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-messaging-mail</artifactId>
    </dependency>
    <!-- web -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-web</artifactId>
    </dependency>

    <!-- 模板库 -->
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>

    <!-- Caffeine 缓存库 - 用于内存限流存储 -->
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>

  </dependencies>

</project>
