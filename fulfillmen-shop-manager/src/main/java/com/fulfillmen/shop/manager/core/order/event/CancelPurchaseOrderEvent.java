/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.fulfillmen.shop.common.context.OrderContext;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 取消采购订单事件
 *
 * <pre>
 * 当采购订单取消时发布此事件，用于：
 * 1. 触发异步的供应商订单取消流程
 * 2. 发送订单取消通知给用户
 * 3. 记录订单取消日志
 * 4. 更新订单统计数据
 * 5. 集成第三方系统（如ERP、WMS等）
 * 6. 触发取消采购流程
 *
 * 架构定位：
 * - 事件驱动架构的核心事件之一
 * - 连接订单取消和后续业务流程的桥梁
 * - 支持异步处理以提高系统响应性能
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/22
 * @description: todo
 * @since 1.0.0
 */
@Getter
public class CancelPurchaseOrderEvent extends ApplicationEvent {

    /**
     * 事件类型
     */
    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.ORDER_CANCELLED;

    /**
     * 订单上下文信息 - 包含完整的订单创建信息
     */
    private final OrderContext orderContext;

    /**
     * 事件创建时间
     */
    private final LocalDateTime eventTime;

    /**
     * 租户ID - 用于多租户环境下的事件处理
     */
    private final String tenantId;

    /**
     * 用户ID - 创建订单的用户
     */
    private final String userId;

    /**
     * 事件来源 - 标识事件的来源系统或模块
     */
    private final String eventSource;

    /**
     * 取消原因
     */
    private final String cancelReason;

    public CancelPurchaseOrderEvent(OrderContext orderContext, LocalDateTime eventTime, String tenantId, String userId, String eventSource, String cancelReason) {
        super(orderContext);
        this.orderContext = orderContext;
        this.eventTime = eventTime;
        this.tenantId = tenantId;
        this.userId = userId;
        this.eventSource = eventSource;
        this.cancelReason = cancelReason;
    }

    // 辅助方式 获取订单ID
    public Long getOrderId() {
        return orderContext.getPurchaseOrder().getId();
    }

    // 辅助方式 获取订单号
    public String getPurchaseOrderNo() {
        return orderContext.getPurchaseOrder().getPurchaseOrderNo();
    }

    // 辅助方式 获取供应商订单数量
    public int getSupplierOrderCount() {
        return orderContext.getSupplierOrders().size();
    }

    // 辅助方式 获取订单总金额
    public BigDecimal getTotalAmount() {
        return orderContext.getPurchaseOrder().getCustomerTotalAmount();
    }

    // 辅助方式 判断是否为多供应商订单
    public boolean isMultipleSupplierOrder() {
        return orderContext.getSupplierOrders().size() > 1;
    }

    // 获取事件的唯一标识
    public String getEventId() {
        return String.format("%s-%s-%d", eventType.name(), getPurchaseOrderNo(), eventTime.toEpochSecond(java.time.ZoneOffset.UTC));
    }

    // 获取用户友好的事件描述
    public String getEventDescription() {
        return String.format("订单取消成功：采购订单号=%s，供应商订单数=%d，总金额=%s",
            getPurchaseOrderNo(), getSupplierOrderCount(), getTotalAmount());
    }

    // 是否为用户取消
    public boolean isUserCancelled() {
        return "USER".equals(cancelReason);
    }

    // 是否为系统取消
    public boolean isSystemCancelled() {
        return "SYSTEM".equals(cancelReason);
    }

    // 是否为超时取消
    public boolean isTimeoutCancelled() {
        return "TIMEOUT".equals(cancelReason);
    }

    // 获取事件的优先级（用于事件处理排序）
    public EventPriority getPriority() {
        // 系统取消订单或用户取消订单优先级更高
        if (isSystemCancelled() || isUserCancelled()) {
            return EventPriority.HIGH;
        }
        return EventPriority.MEDIUM;
    }

    /**
     * 事件优先级枚举
     */
    public enum EventPriority {

        LOW(1, "低优先级"),
        MEDIUM(2, "中等优先级"),
        HIGH(3, "高优先级"),
        URGENT(4, "紧急优先级");

        private final int level;
        private final String description;

        EventPriority(int level, String description) {
            this.level = level;
            this.description = description;
        }

        public int getLevel() {
            return level;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public String toString() {
        return String.format("CancelPurchaseOrderEvent{eventId='%s', purchaseOrderNo='%s', tenantId='%s', userId='%s', " +
            "supplierOrderCount=%d, totalAmount=%s, eventTime=%s, cancelReason='%s'}",
            getEventId(), getPurchaseOrderNo(), tenantId, userId,
            getSupplierOrderCount(), getTotalAmount(), eventTime, cancelReason);
    }
}
