/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

/**
 * 取消采购订单同步 WMS 处理器
 *
 * <AUTHOR>
 * @date 2025/7/22
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CancelPurchaseOrderSyncWmsHandler {

    private final TzOrderSupplierMapper orderSupplierMapper;
    private final IWmsManager wmsManager;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TransactionTemplate transactionTemplate;

    public void handle(OrderContext context) {
        log.info("开始取消 wms 订单，采购订单号: {}", context.getPurchaseOrderNo());
        try {
            syncCancelWmsOrders(context);
            log.info("取消 wms 订单完成 : [{}] ", context.getPurchaseOrderNo());
        } catch (Exception e) {
            log.error("取消 wms 订单失败 : [{}] ", context.getPurchaseOrderNo(), e);
        }
    }

    private List<TzOrderSupplier> getWmsOrders(OrderContext context) {
        List<Long> supporterOrderIds = context.getSupplierOrders().stream()
            .map(TzOrderSupplier::getId)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(supporterOrderIds)) {
            return Lists.newArrayList();
        }

        return orderSupplierMapper.selectList(
            new LambdaQueryWrapper<TzOrderSupplier>()
                .in(TzOrderSupplier::getId, supporterOrderIds)
                .isNotNull(TzOrderSupplier::getWmsPurchaseOrderNo)
                .ne(TzOrderSupplier::getWmsSyncStatus, OrderSupplierSyncStatusEnums.SYNCED)
        );
    }

    private void syncCancelWmsOrders(OrderContext context) {
        log.info("开始取消 wms 订单，采购订单号: {}", context.getPurchaseOrderNo());
        String purchaseOrderNo = context.getPurchaseOrder().getPurchaseOrderNo();
        List<TzOrderSupplier> wmsOrders = getWmsOrders(context);
        if (CollectionUtils.isEmpty(wmsOrders)) {
            log.info("没有需要取消的 wms 订单，采购订单号: {}", purchaseOrderNo);
            return;
        }

        // 获取当前用户的 WMS 账户信息
        String cusCode = UserContextHolder.getWmsCusCodeOrTenantCusCode();

        if (!StringUtils.hasText(cusCode)) {
            log.error("无法获取 cusCode , 找不到 WMS 账户信息，无法取消 Wms 采购订单。请检查一下配置信息。");
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.UNABLE_RETRIEVE_WMS_ACCOUNT);
        }

        // 调用 WMS API 取消订单 取消失败列表
        Optional<List<String>> cancelFailed = wmsManager.cancelWmsPurchaseOrder(purchaseOrderNo, cusCode);
        // 更新供应商订单状态
        if (cancelFailed.isEmpty()) {
            log.error("WMS订单取消成功: {}", cancelFailed);
            transactionTemplate.executeWithoutResult(
                status -> {
                    wmsOrders.forEach(order -> {
                        order.setStatus(TzOrderSupplierStatusEnum.CANCELLED);
                        orderSupplierMapper.updateById(order);
                        log.info("WMS订单状态更新成功: {}", order.getWmsPurchaseOrderNo());
                    });
                    // 取消的订单都成功了，将更新 purchase
                    TzOrderPurchase purchaseOrder = context.getPurchaseOrder();
                    purchaseOrder.setOrderStatus(TzOrderPurchaseStatusEnum.ORDER_CANCELLED);
                    tzOrderPurchaseMapper.updateById(purchaseOrder);
                }
            );
        } else {
            // 否则仅更新供应商订单状态
            List<String> failedList = cancelFailed.get();

        }
    }
}
