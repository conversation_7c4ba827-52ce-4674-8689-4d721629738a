/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.fulfillmen.shop.common.context.OrderContext;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 订单创建事件
 *
 * <pre>
 * 当采购订单创建完成时发布此事件，用于：
 * 1. 触发异步的供应商订单创建流程
 * 2. 发送订单创建通知给用户
 * 3. 记录订单创建日志
 * 4. 更新订单统计数据
 * 5. 集成第三方系统（如ERP、WMS等）
 * 6. 触发采购流程
 *
 * 架构定位：
 * - 事件驱动架构的核心事件之一
 * - 连接订单创建和后续业务流程的桥梁
 * - 支持异步处理以提高系统响应性能
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 订单创建事件，当采购订单创建完成时触发
 * @since 1.0.0
 */
@Getter
public class OrderCreatedEvent extends ApplicationEvent {

    /**
     * 事件类型
     */
    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.ORDER_CREATED;

    /**
     * 订单上下文信息 - 包含完整的订单创建信息
     */
    private final OrderContext orderContext;

    /**
     * 事件创建时间
     */
    private final LocalDateTime eventTime;

    /**
     * 租户ID - 用于多租户环境下的事件处理
     */
    private final String tenantId;

    /**
     * 用户ID - 创建订单的用户
     */
    private final String userId;

    /**
     * 事件来源 - 标识事件的来源系统或模块
     */
    private final String eventSource;

    /**
     * 构造函数
     *
     * @param orderContext 订单上下文
     * @param tenantId     租户ID
     * @param userId       用户ID
     * @param eventSource  事件来源
     */
    public OrderCreatedEvent(OrderContext orderContext, String tenantId, String userId, String eventSource) {
        super(orderContext);
        this.orderContext = orderContext;
        this.tenantId = tenantId;
        this.userId = userId;
        this.eventSource = eventSource;
        this.eventTime = LocalDateTime.now();
    }

    /**
     * 简化构造函数
     *
     * @param orderContext 订单上下文
     * @param tenantId     租户ID
     * @param userId       用户ID
     */
    public OrderCreatedEvent(OrderContext orderContext, String tenantId, String userId) {
        this(orderContext, tenantId, userId, "ORDER_SERVICE");
    }

    /**
     * 获取采购订单号
     */
    public String getPurchaseOrderNo() {
        return orderContext.getPurchaseOrder().getPurchaseOrderNo();
    }

    /**
     * 获取采购订单ID
     */
    public Long getPurchaseOrderId() {
        return orderContext.getPurchaseOrder().getId();
    }

    /**
     * 获取供应商订单数量
     */
    public int getSupplierOrderCount() {
        return orderContext.getSupplierOrders().size();
    }

    /**
     * 获取订单总金额
     */
    public BigDecimal getTotalAmount() {
        return orderContext.getPurchaseOrder().getCustomerTotalAmount();
    }

    /**
     * 判断是否为多供应商订单
     */
    public boolean isMultipleSupplierOrder() {
        return orderContext.getSupplierOrders().size() > 1;
    }

    /**
     * 获取事件的唯一标识
     */
    public String getEventId() {
        return String.format("%s-%s-%d", eventType.name(), getPurchaseOrderNo(), eventTime.toEpochSecond(java.time.ZoneOffset.UTC));
    }

    /**
     * 获取用户友好的事件描述
     */
    public String getEventDescription() {
        return String.format("订单创建成功：采购订单号=%s，供应商订单数=%d，总金额=%s",
            getPurchaseOrderNo(), getSupplierOrderCount(), getTotalAmount());
    }

    /**
     * 获取事件的优先级（用于事件处理排序）
     */
    public EventPriority getPriority() {
        // 多供应商订单或高金额订单优先级更高
        if (isMultipleSupplierOrder() || getTotalAmount().compareTo(BigDecimal.valueOf(10000)) > 0) {
            return EventPriority.HIGH;
        }
        return EventPriority.MEDIUM;
    }

    /**
     * 事件优先级枚举
     */
    public enum EventPriority {

        LOW(1, "低优先级"),
        MEDIUM(2, "中等优先级"),
        HIGH(3, "高优先级"),
        URGENT(4, "紧急优先级");

        private final int level;
        private final String description;

        EventPriority(int level, String description) {
            this.level = level;
            this.description = description;
        }

        public int getLevel() {
            return level;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public String toString() {
        return String.format("OrderCreatedEvent{eventId='%s', purchaseOrderNo='%s', tenantId='%s', userId='%s', " +
            "supplierOrderCount=%d, totalAmount=%s, eventTime=%s, priority=%s}",
            getEventId(), getPurchaseOrderNo(), tenantId, userId,
            getSupplierOrderCount(), getTotalAmount(), eventTime, getPriority());
    }
}
