/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository;

import com.baomidou.mybatisplus.extension.repository.IRepository;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;

/**
 * 订单仓储层
 *
 * <AUTHOR>
 * @date 2025/6/30 14:49
 * @description: 订单数据访问层接口，负责订单相关的数据持久化操作
 * @since 1.0.0
 */
public interface TzOrderPurchaseRepository extends IRepository<TzOrderPurchase> {

    /**
     * 创建采购订单
     *
     * @param orderContext 订单上下文
     */
    void createPurchaseOrder(OrderContext orderContext);

    /**
     * 根据采购订单ID获取采购订单
     * <pre>
     * 忽略租户ID, 查询订单主键
     * </pre>
     *
     * @param purchaseOrderId 采购订单ID
     * @return 采购订单
     */
    TzOrderPurchase getByIdAndIgnoreTenantId(Long purchaseOrderId);

}
