/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.convert;

import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.mapstruct.Mapper;

/**
 * Alibaba 订单详情转换
 *
 * <AUTHOR>
 * @date 2025/7/21 22:57
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface AlibabaOrderDetailConvert {

    /**
     * 转换订单详情
     *
     * @param orderDetail 订单详情响应
     * @return OrderContext
     */
    default OrderContext convertOrderContext(OrderDetail orderDetail) {
        // 订单的基础信息
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();

        return OrderContext.builder()

            .build();
    }

    /**
     * 创建 供应商订单 根据 1688 订单
     * <pre>
     * 注意：
     * 如果需要将 1688 订单 转换成 TzOrderSupplier 订单
     * 需要两个接口的数据，一个 1688 订单详情 + wms 订单详情
     * </pre>
     *
     * @param baseInfo    订单基础信息
     * @param orderDetail 订单详情
     * @return 供应商订单
     */
    default TzOrderSupplier createTzOrderSupplier(TradeBaseInfo baseInfo, OrderDetail orderDetail) {
        OrderStatusEnums orderStatusEnums = OrderStatusEnums.fromCode(baseInfo.getStatus());
        boolean isPaid = true;
        if (OrderStatusEnums.WAIT_BUYER_PAY.equals(orderStatusEnums)) {
            // 已支付
            isPaid = false;
        }

        TzOrderSupplier tzOrderSupplier = TzOrderSupplier.builder()
            .id(0L)
            .purchaseOrderId(0L)
            .supplierOrderNo("")
            .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
            .metadataJson(JacksonUtil.toJsonString(orderDetail))
            .supplierId(baseInfo.getSellerId())
            .supplierName(baseInfo.getSellerContact().getImInPlatform())
            .supplierShopName(baseInfo.getSellerContact().getShopName())
            // 判断是等待支付还是已支付
            .status(TzOrderSupplierStatusEnum.PENDING_PAYMENT)
            .platformOrderNo(baseInfo.getIdOfStr())
            .externalSyncStatus(OrderSupplierSyncStatusEnums.SYNCED)
            // 这个是产品总价 不含运费
            .payableGoodsAmount(baseInfo.getSumProductPayment())
            // 这个是运费
            .payableFreightAmount(baseInfo.getShippingFee())
            // 实付价格
            .payableAmountTotal(baseInfo.getTotalAmount())
            .payablePlusDiscountAmount(baseInfo.getDiscount() != null ? baseInfo.getDiscount().divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP) : BigDecimal.ZERO)
            // 实付
            .actualPaymentAmount(baseInfo.getTotalAmount())
            .actualPaymentGoodsAmount(baseInfo.getSumProductPayment())
            .actualPaymentFreightAmount(baseInfo.getShippingFee())
            .lineItemCount(orderDetail.getProductItems().size())
            // 订单创建时间
            .gmtCreated(baseInfo.getCreateTime())
            // 订单修改时间
            .gmtModified(baseInfo.getModifyTime())
            .build();
        return tzOrderSupplier;
    }

}
