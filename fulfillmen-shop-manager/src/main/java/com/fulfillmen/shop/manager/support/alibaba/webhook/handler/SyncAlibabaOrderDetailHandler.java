/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 创建 1688 同步过来的订单
 *
 * <AUTHOR>
 * @date 2025/7/22 21:50
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SyncAlibabaOrderDetailHandler {

    private final IWmsManager wmsManager;

    private final TzOrderItemMapper tzOrderItemMapper;

    private final TzOrderSupplierMapper tzOrderSupplierMapper;

    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;

    /**
     * 处理 1688 订单数据 ，同步入库。
     */
    public void doHandler(OrderDetail orderDetail) {
        /*
         情况1: 如果 shop.fulfillmen.com 旧版数据，创建的订单。则非 nayasource 订单数据。
         那么为了，能够处理 wms 订单数据流转，需要在 nayasource 创建对应的订单数据。
         但是为了能够处理订单数据，需要将 wms_cus_code 和 wms_cus_id 保存下来。以便用户注册到 nayasource 后，可以将订单数据同步过来
         */
        // 获取对应的 wms 订单数据来同步 订单数据
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        // 查询 wms 订单
        String orderId = baseInfo.getIdOfStr();
        TzOrderSupplier tzOrderSupplier = findByOrderId(orderId);
        if (Objects.isNull(tzOrderSupplier)) {
            List<WmsPurchaseOrderDetailsRes> wmsPurchaseOrderDetailsRes = wmsManager.queryOrderDetail(PurchaseOrderDetailReq.builder().orderId(orderId).build());
            if (CollectionUtils.isEmpty(wmsPurchaseOrderDetailsRes)) {
                log.info("wms 订单不存在，无法同步，订单号: {}", orderId);
                return;
            }
            // 如果 不为空，默认根据 orderId 查询只有一笔订单
            WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = wmsPurchaseOrderDetailsRes.get(0);
        }
    }

    private TzOrderSupplier findByOrderId(String orderId) {
        // 1. 如果订单不存在，可能需要同步处理采购单。
        // 查询供应商订单与 1688 订单对应的 id
        LambdaQueryWrapper<TzOrderSupplier> orderSupplierLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderSupplierLambdaQueryWrapper.eq(TzOrderSupplier::getPlatformOrderId, orderId).last("LIMIT 1");
        TzOrderSupplier orderSupplier = this.tzOrderSupplierMapper.selectOne(orderSupplierLambdaQueryWrapper);
        // 1. 如果供应商的订单为空，则非 nayasource 订单，需要进行同步采购单处理流程。
        if (orderSupplier == null) {
            log.info("当前订单非当前系统创建，存在两种情况: 1、1688 创建订单. 2、旧版 shop 创建订单 : [{}] ", orderId);
            return null;
        }
        return orderSupplier;
    }

}
