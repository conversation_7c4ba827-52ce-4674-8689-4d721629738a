/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.TzProductSkuDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseBuyerTypeEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import com.fulfillmen.shop.manager.core.repository.TzOrderPurchaseRepository;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderDataSyncService;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeNativeLogisticsInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 订单数据同步服务实现
 *
 * <pre>
 * 实现要点：
 * 1. 数据存在性检查和完整性验证
 * 2. 缺失数据的智能补齐
 * 3. 新旧版本数据的兼容处理
 * 4. 数据关联关系的正确建立
 * 5. 状态和时间戳的合理初始化
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/25 16:40
 * @description 订单数据同步服务实现
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderDataSyncServiceImpl implements OrderDataSyncService {

    private final TzOrderPurchaseRepository orderPurchaseRepository;
    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TzOrderItemMapper orderItemMapper;
    private final IProductSyncService productSyncService;
//    private final IdGenerator idGenerator;

    @Override
    public OrderDataIntegrityResult checkOrderDataIntegrity(String orderId) {
        log.debug("开始检查订单数据完整性: orderId={}", orderId);
        // 1. 检查当前的供应商订单
        TzOrderSupplier existingSupplierOrders = findSupplierOrderByExternalId(orderId);
        if (Objects.isNull(existingSupplierOrders)) {
            return new OrderDataIntegrityResult(
                false, false, false, false, true,
                null, Collections.emptyList(), Collections.emptyList()
            );
        }

        // 1. 检查采购订单
        TzOrderPurchase purchaseOrder = findPurchaseOrderByPurchaseOrderId(existingSupplierOrders.getPurchaseOrderId());
        boolean hasPurchaseOrder = purchaseOrder != null;

        // 2. 获取采购单下的供应商订单
        List<TzOrderSupplier> supplierOrders = Lists.newArrayList();

        boolean hasSupplierOrders = false;
        if (hasPurchaseOrder) {
            supplierOrders = findSupplierOrdersByPurchaseOrderId(existingSupplierOrders.getPurchaseOrderId());
            hasSupplierOrders = !CollectionUtils.isEmpty(supplierOrders);
        }

        // 3. 检查订单项
        List<TzOrderItem> orderItems = Lists.newArrayList();
        boolean hasOrderItems = false;

        if (hasPurchaseOrder) {
            orderItems = findOrderItemsByPurchaseOrderId(purchaseOrder.getId());
            hasOrderItems = !CollectionUtils.isEmpty(orderItems);
        }

        // 4. 判断数据完整性
        boolean isDataComplete = hasPurchaseOrder && hasSupplierOrders && hasOrderItems;
        boolean isLegacyData = !isDataComplete;

        OrderDataIntegrityResult result = new OrderDataIntegrityResult(
            hasPurchaseOrder, hasSupplierOrders, hasOrderItems, isDataComplete, isLegacyData,
            purchaseOrder, supplierOrders, orderItems
        );

        log.info("订单数据完整性检查完成: orderId={}, result={}", orderId, result);
        return result;
    }

    /**
     * 根据外部订单ID查询供应商订单
     *
     * @param purchaseOrderId 采购订单ID
     * @return 供应商订单列表
     */
    private List<TzOrderSupplier> findSupplierOrdersByPurchaseOrderId(Long purchaseOrderId) {
        return this.orderSupplierMapper.listByPurchaseOrderIdAndIgnoreTenantId(purchaseOrderId);
    }

    /**
     * 根据 id 查询采购订单
     *
     * @param purchaseOrderId 采购订单ID
     * @return 采购订单
     */
    private TzOrderPurchase findPurchaseOrderByPurchaseOrderId(Long purchaseOrderId) {
        return this.orderPurchaseRepository.getByIdAndIgnoreTenantId(purchaseOrderId);
    }

    @Override
    public OrderContextRecord syncAndCompleteOrderData(String orderId, OrderDataIntegrityResult integrityResult,
        OrderDetail orderDetail, List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        log.info("开始同步和补齐订单数据: orderId={}, needsSync={}", orderId, integrityResult.needsDataSync());

        if (integrityResult.isDataComplete()) {
            // 数据完整，直接返回现有数据
            log.debug("订单数据完整，直接使用现有数据: orderId={}", orderId);
            return buildOrderContextRecord(integrityResult, orderDetail, wmsOrderDetails);
        }

        // 数据不完整，需要补齐
        return syncMissingOrderData(orderId, integrityResult, orderDetail, wmsOrderDetails);
    }

    /**
     * 同步缺失的订单数据
     */
    private OrderContextRecord syncMissingOrderData(String orderId, OrderDataIntegrityResult integrityResult,
        OrderDetail orderDetail, List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        log.info("开始补齐缺失的订单数据: orderId={}, missing={}", orderId, integrityResult.getMissingDataDescription());

        TzOrderPurchase purchaseOrder = integrityResult.existingPurchaseOrder();
        List<TzOrderSupplier> supplierOrders = new ArrayList<>(integrityResult.existingSupplierOrders());
        List<TzOrderItem> orderItems = new ArrayList<>(integrityResult.existingOrderItems());

        // 获取WMS订单详情（如果存在）
        WmsPurchaseOrderDetailsRes wmsOrderDetail = CollectionUtils.isEmpty(wmsOrderDetails) ? null : wmsOrderDetails.get(0);

        try {
            // 1. 补齐采购订单
            if (!integrityResult.hasPurchaseOrder()) {
                purchaseOrder = createAndSavePurchaseOrder(orderDetail, wmsOrderDetail);
                log.info("创建采购订单成功: orderId={}, purchaseOrderId={}", orderId, purchaseOrder.getId());
            }

            // 2. 补齐供应商订单
            if (!integrityResult.hasSupplierOrders()) {
                supplierOrders = createAndSaveSupplierOrders(purchaseOrder.getId(), orderDetail, wmsOrderDetail);
                log.info("创建供应商订单成功: orderId={}, supplierOrderCount={}", orderId, supplierOrders.size());
            }

            // 3. 补齐订单项
            if (!integrityResult.hasOrderItems() && !CollectionUtils.isEmpty(supplierOrders)) {
                Long supplierOrderId = supplierOrders.get(0).getId();
                orderItems = createAndSaveOrderItems(purchaseOrder.getId(), supplierOrderId, orderDetail, wmsOrderDetail);
                log.info("创建订单项成功: orderId={}, orderItemCount={}", orderId, orderItems.size());
            }

            log.info("订单数据补齐完成: orderId={}, purchaseOrderId={}, supplierOrderCount={}, orderItemCount={}",
                orderId, purchaseOrder.getId(), supplierOrders.size(), orderItems.size());

            return OrderContextRecord.builder()
                .orderDetail(orderDetail)
                .wmsPurchaseOrderDetailsRes(wmsOrderDetails)
                .tzOrderPurchase(purchaseOrder)
                .tzOrderSuppliers(supplierOrders)
                .tzOrderItems(orderItems)
                .build();

        } catch (Exception e) {
            log.error("订单数据补齐失败: orderId={}", orderId, e);
            throw new RuntimeException("订单数据补齐失败", e);
        }
    }

    /**
     * 创建并保存采购订单
     */
    private TzOrderPurchase createAndSavePurchaseOrder(OrderDetail orderDetail,
        WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        TzOrderPurchase purchaseOrder = createPurchaseOrderData(orderDetail, wmsOrderDetail);
        orderPurchaseRepository.save(purchaseOrder);
        return purchaseOrder;
    }

    /**
     * 创建并保存供应商订单
     */
    private List<TzOrderSupplier> createAndSaveSupplierOrders(Long purchaseOrderId,
        OrderDetail orderDetail,
        WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        List<TzOrderSupplier> supplierOrders = createSupplierOrderData(purchaseOrderId, orderDetail, wmsOrderDetail);
        if (!CollectionUtils.isEmpty(supplierOrders)) {
            orderSupplierMapper.insertBatch(supplierOrders);
        }
        return supplierOrders;
    }

    /**
     * 创建并保存订单项
     */
    private List<TzOrderItem> createAndSaveOrderItems(Long purchaseOrderId,
        Long supplierOrderId,
        OrderDetail orderDetail,
        WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        List<TzOrderItem> orderItems = createOrderItemData(purchaseOrderId, supplierOrderId, orderDetail, wmsOrderDetail);
        if (!CollectionUtils.isEmpty(orderItems)) {
            orderItemMapper.insertBatch(orderItems);
        }
        return orderItems;
    }

    @Override
    public TzOrderPurchase createPurchaseOrderData(OrderDetail orderDetail,
        WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        TradeNativeLogisticsInfo logistics = orderDetail.getNativeLogistics();
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        return TzOrderPurchase.builder()
            .id(idGenerator.generate())
            .purchaseOrderNo(baseInfo.getIdOfStr())
            // 旧版数据默认买家ID为0 ，也许不是平台的用户。
            .buyerId(0L)
            .buyerType(TzOrderPurchaseBuyerTypeEnums.WMS)
            .orderStatus(TzOrderPurchaseStatusEnum.PAYMENT_PENDING)
            .orderDate(baseInfo.getCreateTime())
            // 用户支付的产品总价
            .customerGoodsAmount(wmsOrderDetail.getProductSalesTotalAmount())
            // 用户支付运费
            .customerTotalFreight(wmsOrderDetail.getShippingFee())
            // 用户支付产品+运费
            .customerTotalAmount(wmsOrderDetail.getTotal())
            // 用户支付服务费
            .serviceFee(wmsOrderDetail.getServiceFee() != null ? wmsOrderDetail.getServiceFee() : BigDecimal.ZERO)
            // 总优惠换算成元
            .payableDiscountAmount(baseInfo.getDiscount().multiply(BigDecimal.valueOf(100)))
            // 旧版数据默认汇率为0 ， 为实时汇率方式计算
            .exchangeRateSnapshot(BigDecimal.ZERO)
            // 收货地址信息 2025 年 07 月 28 日 17:42:46 后期废弃
            .deliveryAddress(logistics != null ? logistics.getAddress() : "")
            .postalCode(logistics != null ? logistics.getZip() : "")
            .province(logistics != null ? logistics.getProvince() : "")
            .city(logistics != null ? logistics.getCity() : "")
            .district(logistics != null ? logistics.getArea() : "")
            .consigneeName(logistics != null ? logistics.getContactPerson() : "")
            .consigneePhone(logistics != null ? logistics.getMobile() : "")
            // 统计信息
            // 默认一个供应商
            .supplierCount(1)
            .lineItemCount(orderDetail.getProductItems() != null ? orderDetail.getProductItems().size() : 0)
            .totalQuantity(calculateTotalQuantity(orderDetail.getProductItems()))
            .completedSupplierCount(0)
            // 系统字段
            .build();
    }

    @Override
    public List<TzOrderSupplier> createSupplierOrderData(Long purchaseOrderId,
        OrderDetail orderDetail,
        WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        List<TzOrderSupplier> supplierOrders = new ArrayList<>();
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        // 为兼容旧版，创建一个默认的供应商订单
        TzOrderSupplier supplierOrder = TzOrderSupplier.builder()
            .id(idGenerator.generate())
            .purchaseOrderId(purchaseOrderId)
            .supplierOrderNo(generateSupplierOrderNo())
            .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
            .supplierId(baseInfo.getSellerId() != null ? baseInfo.getSellerId() : "unknown")
            .supplierName(baseInfo.getSellerLoginId() != null ? baseInfo.getSellerLoginId() : "未知供应商")
            .platformOrderId(baseInfo.getIdOfStr())
            .status(TzOrderSupplierStatusEnum.PENDING_PAYMENT)
            .orderDate(baseInfo.getCreateTime())
            .payableGoodsAmount(baseInfo.getSumProductPayment())
            .payableFreightAmount(baseInfo.getShippingFee())
            .serviceFee(wmsOrderDetail != null ? wmsOrderDetail.getServiceFee() : BigDecimal.ZERO)
            .payableAmountTotal(baseInfo.getTotalAmount())
            .payableDiscountAmount(baseInfo.getDiscount())
            .lineItemCount(orderDetail.getProductItems() != null ? orderDetail.getProductItems().size() : 0)
            .completedItemCount(0)
            .build();

        supplierOrders.add(supplierOrder);
        return supplierOrders;
    }

    @Override
    public List<TzOrderItem> createOrderItemData(Long purchaseOrderId, Long supplierOrderId,
        OrderDetail orderDetail, WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        List<TzOrderItem> orderItems = new ArrayList<>();
        List<TradeProductItem> productItems = orderDetail.getProductItems();
        if (CollectionUtils.isEmpty(productItems)) {
            return orderItems;
        }
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        for (int i = 0; i < productItems.size(); i++) {
            TradeProductItem productItem = productItems.get(i);
            // 检查是否为单品
            boolean isSingleItem = productSyncService.isSingleItem(String.valueOf(productItem.getProductId()));
            // 获取并同步 ProductId
            TzProductDTO productDTO = this.productSyncService.getOrSyncProductByPlatformId(String.valueOf(productItem.getProductId()));
            Long spuId = productDTO != null ? productDTO.getId() : 0L;
            Long skuId = productDTO != null && productDTO.getIsSingleItem().equals(TzProductSpuSingleItemEnum.YES) ? productDTO.getSkuList().get(0).getId() : 0L;
            // 商品的物流信息
            TzOrderItemLogisticsStatusEnum logisticsStatusEnum = TzOrderItemLogisticsStatusEnum.getByCode(productItem.getLogisticsStatus());
            Map<Long, TzProductSkuDTO> skuMap = productDTO != null ? productDTO.getSkuList().stream().collect(Collectors.toMap(TzProductSkuDTO::getId, sku -> sku)) : Collections.emptyMap();
            // 获取 sku 信息
            TzProductSkuDTO sku = skuMap.get(skuId);
            List<AttrJson> specs;
            if (Objects.nonNull(sku)) {
                specs = sku.getSpecs();
            } else {
                if (!isSingleItem) {
                    specs = new ArrayList<>();
                    productItem.getSkuInfos().forEach(skuInfo -> {
                        specs.add(AttrJson.builder().attrKey(skuInfo.getName()).attrValue(skuInfo.getValue()).build());
                    });
                } else {
                    specs = null;
                }
            }

            // 创建订单项
            TzOrderItem orderItem = TzOrderItem.builder()
                .id(idGenerator.generate())
                .purchaseOrderId(purchaseOrderId)
                .supplierOrderId(supplierOrderId)
                .lineNumber(i + 1)
                .productSpuId(spuId)
                // 如果是单品 id
                .productSkuId(skuId)
                .platformProductId(productItem.getProductId() != null ? productItem.getProductId().toString() : "")
                .platformSkuId(productItem.getSkuId() != null ? productItem.getSkuId().toString() : "")
                .platformSpecId(productItem.getSpecId() != null ? productItem.getSpecId() : "")
                .platformOrderId(orderDetail.getBaseInfo().getIdOfStr())
                .platformMetadata(JacksonUtil.toJsonString(productItem))
                .platformSnapshotUrl(productItem.getProductSnapshotUrl())
                .productTitle(Objects.nonNull(productDTO) ? productDTO.getTitle() : productItem.getName())
                .productTitleEn(Objects.nonNull(productDTO) ? productDTO.getTitleTrans() : null)
                .productLink(productItem.getProductSnapshotUrl())
                .skuSpecs(specs)
                .productImageUrl(productItem.getProductImgUrl() == null || CollectionUtils.isEmpty(List.of(productItem.getProductImgUrl())) ? "" : productItem.getProductImgUrl()[0])
                .price(productItem.getPrice())
                .quantity(productItem.getQuantity())
                // 商品小计
                .totalAmount(productItem.getItemAmount())
                .unit(productItem.getUnit())
                // 这里要修改
                // .status(TzOrderItemStatusEnum.PENDING)
                // 默认未发货
                .logisticsStatus(logisticsStatusEnum)
                // 收货时间
                .completedDatetime(productItem.getGmtCompleted())
                .isSingleItem(isSingleItem ? TzProductSpuSingleItemEnum.YES : TzProductSpuSingleItemEnum.NO)
                .gmtCreated(productItem.getGmtCreate())
                .gmtModified(productItem.getGmtModified())
                .build();

            orderItems.add(orderItem);
        }

        return orderItems;
    }

    /**
     * 构建订单上下文记录
     */
    private OrderContextRecord buildOrderContextRecord(OrderDataIntegrityResult integrityResult,
        OrderDetail orderDetail,
        List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        return OrderContextRecord.builder()
            .orderDetail(orderDetail)
            .wmsPurchaseOrderDetailsRes(wmsOrderDetails)
            .tzOrderPurchase(integrityResult.existingPurchaseOrder())
            .tzOrderSuppliers(integrityResult.existingSupplierOrders())
            .tzOrderItems(integrityResult.existingOrderItems())
            .build();
    }

    /**
     * 根据外部订单ID查找供应商订单 一个 1688 订单只会对应一个 供应商订单
     */
    private TzOrderSupplier findSupplierOrderByExternalId(String orderId) {
        return orderSupplierMapper.findByExternalPlatformOrderIdAndIgnoreTenantId(orderId);
    }

    /**
     * 根据采购订单ID查找订单项
     */
    private List<TzOrderItem> findOrderItemsByPurchaseOrderId(Long purchaseOrderId) {
        LambdaQueryWrapper<TzOrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TzOrderItem::getPurchaseOrderId, purchaseOrderId);
        return orderItemMapper.selectList(wrapper);
    }

    /**
     * 计算商品总数量
     */
    private Integer calculateTotalQuantity(List<TradeProductItem> productItems) {
        if (CollectionUtils.isEmpty(productItems)) {
            return 0;
        }
        return productItems.stream()
            .mapToInt(item -> item.getQuantity() != null ? item.getQuantity().intValue() : 0)
            .sum();
    }

    /**
     * 生成供应商订单号
     */
    private String generateSupplierOrderNo() {
        return "SO" + System.currentTimeMillis();
    }
}
