/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContext;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import com.fulfillmen.shop.frontend.util.OrderStatusUtil;
import com.fulfillmen.shop.frontend.vo.UserOrderDetailVO;
import com.fulfillmen.shop.frontend.vo.UserPurchaseOrderListVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 前端采购订单转换器
 *
 * <AUTHOR>
 * @date 2025/7/7 16:16
 * @description 处理采购订单与前端VO之间的转换
 */
@Mapper
public interface FrontendOrderPurchaseConvert {

    FrontendOrderPurchaseConvert INSTANCE = Mappers.getMapper(FrontendOrderPurchaseConvert.class);

    /**
     * 转换采购订单分页对象为前端订单列表分页对象
     *
     * @param orderPurchasePage 采购订单分页对象
     * @return 前端订单列表分页对象
     */
    @Mappings({
        @Mapping(source = "current", target = "pageIndex"),
        @Mapping(source = "size", target = "pageSize")
    })
    PageDTO<UserPurchaseOrderListVO> convertToUserPurchaseOrderListVO(Page<TzOrderPurchase> orderPurchasePage);

    /**
     * 转换订单项为前端订单项信息
     *
     * @param orderItem 订单项
     * @return 前端订单项信息
     */
    @Mappings({
        @Mapping(target = "productImage", source = "productImageUrl"),
        @Mapping(target = "id", expression = "java(String.valueOf(orderItem.getId()))"),
        @Mapping(target = "productId", source = "productSpuId"),
        @Mapping(target = "skuId", source = "productSkuId"),
        @Mapping(target = "productTitle", source = "productTitle"),
        @Mapping(target = "productTitleEn", source = "productTitleEn"),
        @Mapping(target = "skuSpecs", source = "skuSpecs"),
        @Mapping(target = "orderedQuantity", source = "quantity"),
        @Mapping(target = "unitOfMeasure", source = "unit"),
        @Mapping(target = "unitPrice", source = "price"),
        @Mapping(target = "unitPriceUsd", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), null))"),
        @Mapping(target = "lineTotalAmount", source = "totalAmount"),
        @Mapping(target = "lineTotalAmountUsd", expression = "java(calculateItemUsdPrice(orderItem.getTotalAmount(), null))"),
        @Mapping(target = "itemStatus", source = "status"),
        @Mapping(target = "itemStatusName", expression = "java(orderItem.getStatus() != null ? orderItem.getStatus().name() : \"\")"),
        @Mapping(target = "available", expression = "java(true)"),
        @Mapping(target = "message", expression = "java(\"\")")
    })
    UserPurchaseOrderListVO.OrderItemInfo convertToOrderItemInfo(TzOrderItem orderItem);

    /**
     * 转换订单项为前端订单项信息（带汇率）
     *
     * @param orderItem    订单项
     * @param exchangeRate 汇率
     * @return 前端订单项信息
     */
    @Mappings({
        @Mapping(target = "productImage", source = "orderItem.productImageUrl"),
        @Mapping(target = "id", expression = "java(String.valueOf(orderItem.getId()))"),
        @Mapping(target = "productId", source = "orderItem.productSpuId"),
        @Mapping(target = "skuId", source = "orderItem.productSkuId"),
        @Mapping(target = "productTitle", source = "orderItem.productTitle"),
        @Mapping(target = "productTitleEn", source = "orderItem.productTitleEn"),
        @Mapping(target = "skuSpecs", source = "orderItem.skuSpecs"),
        @Mapping(target = "orderedQuantity", source = "orderItem.quantity"),
        @Mapping(target = "unitOfMeasure", source = "orderItem.unit"),
        @Mapping(target = "unitPrice", source = "orderItem.price"),
        @Mapping(target = "unitPriceUsd", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), exchangeRate))"),
        @Mapping(target = "lineTotalAmount", source = "orderItem.totalAmount"),
        @Mapping(target = "lineTotalAmountUsd", expression = "java(calculateItemUsdPrice(orderItem.getTotalAmount(), exchangeRate))"),
        @Mapping(target = "itemStatus", source = "orderItem.status"),
        @Mapping(target = "itemStatusName", expression = "java(orderItem.getStatus() != null ? orderItem.getStatus().name() : \"\")"),
        @Mapping(target = "available", expression = "java(true)"),
        @Mapping(target = "message", expression = "java(\"\")")
    })
    UserPurchaseOrderListVO.OrderItemInfo convertToOrderItemInfoWithExchangeRate(TzOrderItem orderItem, BigDecimal exchangeRate);

    /**
     * 转换采购订单为前端订单详情
     *
     * @param tzOrderPurchase 采购订单
     * @return 前端订单详情
     */
    @Mappings({
        @Mapping(target = "orderNo", source = "tzOrderPurchase.purchaseOrderNo"),
        @Mapping(target = "orderStatus", source = "tzOrderPurchase.orderStatus"),
        @Mapping(target = "orderStatusText", expression = "java(getOrderStatusText(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderStatusDescription", expression = "java(getOrderStatusDescription(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderStatusIcon", expression = "java(getOrderStatusIcon(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderStatusColor", expression = "java(getOrderStatusColor(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderStatusTagType", expression = "java(getOrderStatusTagType(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "progressPercentage", expression = "java(getProgressPercentage(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "totalQuantity", source = "tzOrderPurchase.totalQuantity"),
        @Mapping(target = "productTypeCount", source = "tzOrderPurchase.lineItemCount"),
        @Mapping(target = "createTime", source = "tzOrderPurchase.gmtCreated"),
        @Mapping(target = "updateTime", source = "tzOrderPurchase.gmtModified"),
        @Mapping(target = "payTime", source = "tzOrderPurchase.paidDate"),
        @Mapping(target = "deliveryTime", source = "tzOrderPurchase.orderDate"),
        @Mapping(target = "completedTime", source = "tzOrderPurchase.orderCompletedDate"),
        @Mapping(target = "totalAmount", source = "tzOrderPurchase.customerTotalAmount"),
        @Mapping(target = "totalAmountUsd", expression = "java(calculateTotalAmountUsd(tzOrderPurchase.getCustomerTotalAmount(), tzOrderPurchase.getExchangeRateSnapshot()))"),
        @Mapping(target = "shippingFee", source = "tzOrderPurchase.customerTotalFreight"),
        @Mapping(target = "shippingFeeUsd", expression = "java(calculateItemUsdPrice(tzOrderPurchase.getCustomerTotalFreight(), tzOrderPurchase.getExchangeRateSnapshot()))"),
        @Mapping(target = "serviceFeeRate", expression = "java(getTenantServiceFeeRate())"),
        @Mapping(target = "currency", expression = "java(\"CNY\")"),
        @Mapping(target = "buyerMessage", source = "tzOrderPurchase.purchaseNotes"),
        @Mapping(target = "mainProductTitle", expression = "java(\"\")"),
        @Mapping(target = "mainProductTitleEn", expression = "java(\"\")"),
        @Mapping(target = "mainProductImageUrl", expression = "java(\"\")"),
        @Mapping(target = "canCancel", expression = "java(canCancelOrder(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canPay", expression = "java(canPayOrder(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canConfirmReceipt", expression = "java(canConfirmReceipt(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canApplyRefund", expression = "java(canApplyRefund(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canViewTracking", expression = "java(canViewTracking(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderItems", ignore = true)
    })
    UserPurchaseOrderListVO convertToUserPurchaseOrderListVO(TzOrderPurchase tzOrderPurchase);

    /**
     * 转换订单详情
     *
     * @param purchaseOrder 采购订单
     * @param orderItems    订单项列表
     * @return 前端订单详情
     */
    @Mappings({
        @Mapping(target = "orderNo", source = "purchaseOrder.purchaseOrderNo"),
        @Mapping(target = "status", expression = "java(purchaseOrder.getOrderStatus() != null ? purchaseOrder.getOrderStatus().name() : \"\")"),
        @Mapping(target = "createTime", source = "purchaseOrder.gmtCreated"),
        @Mapping(target = "payTime", source = "purchaseOrder.paidDate"),
        @Mapping(target = "shipTime", expression = "java(null)"), // 暂时设为null，后续可从供应商订单获取
        @Mapping(target = "completeTime", source = "purchaseOrder.orderCompletedDate"),
        @Mapping(target = "items", expression = "java(orderItems)"),
        @Mapping(target = "priceInfo", expression = "java(buildPriceInfo(purchaseOrder))"),
        @Mapping(target = "deliveryInfo", expression = "java(buildDeliveryInfo(purchaseOrder))"),
        @Mapping(target = "trackingInfo", expression = "java(null)"), // 暂时设为null，后续实现物流追踪
        @Mapping(target = "actionButtons", expression = "java(buildActionButtons(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "timeline", expression = "java(buildOrderTimeline(purchaseOrder))")
    })
    UserOrderDetailVO convertToUserOrderDetailVO(TzOrderPurchase purchaseOrder, List<UserOrderDetailVO.OrderItemDetailInfo> orderItems);

    /**
     * 转换订单项为前端详情订单项信息
     */
    @Mappings({
        @Mapping(target = "productImage", source = "productImageUrl"),
        @Mapping(target = "productTitle", source = "productTitle"),
        @Mapping(target = "productTitleEn", source = "productTitleEn"),
        @Mapping(target = "productSpecs", expression = "java(convertSkuSpecsToString(orderItem.getSkuSpecs()))"),
        @Mapping(target = "productSpecsEn", expression = "java(convertSkuSpecsToStringEn(orderItem.getSkuSpecs()))"),
        @Mapping(target = "quantity", source = "quantity"),
        @Mapping(target = "arrivedQuantity", expression = "java(0)"), // 暂时设为0，后续可从物流信息获取
        @Mapping(target = "price", source = "price"),
        @Mapping(target = "priceUsd", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), null))"),
        @Mapping(target = "unit", source = "unit"),
        @Mapping(target = "unitEn", source = "unitEn"),
        @Mapping(target = "subtotal", source = "totalAmount"),
        @Mapping(target = "itemStatus", expression = "java(orderItem.getStatus() != null ? orderItem.getStatus().name() : \"\")"),
        @Mapping(target = "itemStatusName", expression = "java(orderItem.getStatus() != null ? orderItem.getStatus().getDesc() : \"\")")
    })
    UserOrderDetailVO.OrderItemDetailInfo convertToOrderItemDetailInfo(TzOrderItem orderItem);

    /**
     * 转换订单项为前端详情订单项信息（带汇率）
     *
     * @param orderItem
     * @param exchangeRate
     * @return
     */
    @Mappings({
        @Mapping(target = "productImage", source = "orderItem.productImageUrl"),
        @Mapping(target = "productTitle", source = "orderItem.productTitle"),
        @Mapping(target = "productTitleEn", source = "orderItem.productTitleEn"),
        @Mapping(target = "productSpecs", expression = "java(convertSkuSpecsToString(orderItem.getSkuSpecs()))"),
        @Mapping(target = "productSpecsEn", expression = "java(convertSkuSpecsToStringEn(orderItem.getSkuSpecs()))"),
        @Mapping(target = "quantity", source = "orderItem.quantity"),
        @Mapping(target = "arrivedQuantity", expression = "java(0)"), // 暂时设为0，后续可从物流信息获取
        @Mapping(target = "price", source = "orderItem.price"),
        @Mapping(target = "priceUsd", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), exchangeRate))"),
        @Mapping(target = "unit", source = "orderItem.unit"),
        @Mapping(target = "unitEn", source = "orderItem.unitEn"),
        @Mapping(target = "subtotal", source = "orderItem.totalAmount"),
        @Mapping(target = "itemStatus", expression = "java(orderItem.getStatus() != null ? orderItem.getStatus().name() : \"\")"),
        @Mapping(target = "itemStatusName", expression = "java(orderItem.getStatus() != null ? orderItem.getStatus().getDesc() : \"\")")
    })
    UserOrderDetailVO.OrderItemDetailInfo convertToOrderItemDetailInfoWithExchangeRate(TzOrderItem orderItem, BigDecimal exchangeRate);

    /**
     * 转换订单项列表为前端订单项列表
     *
     * @param tzOrderItems 订单项列表
     * @return 前端订单项列表
     */
    List<UserPurchaseOrderListVO.OrderItemInfo> convertToOrderItemInfoList(List<TzOrderItem> tzOrderItems);

    // ==================== 状态相关方法 ====================

    /**
     * 获取订单状态显示文本
     */
    default String getOrderStatusText(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getUserFriendlyStatusName(status);
    }

    /**
     * 获取订单状态描述
     */
    default String getOrderStatusDescription(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getUserFriendlyStatusDescription(status);
    }

    /**
     * 获取订单状态图标
     */
    default String getOrderStatusIcon(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getStatusIcon(status);
    }

    /**
     * 获取订单状态颜色
     */
    default String getOrderStatusColor(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getStatusColor(status);
    }

    /**
     * 获取订单状态标签类型
     */
    default String getOrderStatusTagType(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getElementTagType(status);
    }

    /**
     * 获取订单进度百分比
     */
    default Integer getProgressPercentage(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getProgressPercentage(status);
    }

    // ==================== 权限判断方法 ====================

    /**
     * 判断订单是否可以取消
     */
    default Boolean canCancelOrder(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canCancel(status);
    }

    /**
     * 判断订单是否可以支付
     */
    default Boolean canPayOrder(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canPay(status);
    }

    /**
     * 判断订单是否可以确认收货
     */
    default Boolean canConfirmReceipt(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canConfirmReceipt(status);
    }

    /**
     * 判断订单是否可以申请退款
     */
    default Boolean canApplyRefund(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canApplyRefund(status);
    }

    /**
     * 判断订单是否可以查看物流
     */
    default Boolean canViewTracking(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canViewTracking(status);
    }

    // ==================== 价格计算方法 ====================

    /**
     * 计算商品美元价格
     *
     * @param price        人民币价格
     * @param exchangeRate 汇率
     * @return 美元价格
     */
    default BigDecimal calculateItemUsdPrice(BigDecimal price, BigDecimal exchangeRate) {
        if (price == null) {
            return BigDecimal.ZERO;
        }
        if (exchangeRate == null || exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
            // 使用默认汇率 1 CNY = 0.14 USD
            exchangeRate = new BigDecimal("0.14");
        }
        return price.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算订单总美元金额
     *
     * @param totalAmount  人民币总金额
     * @param exchangeRate 汇率
     * @return 美元总金额
     */
    default BigDecimal calculateTotalAmountUsd(BigDecimal totalAmount, BigDecimal exchangeRate) {
        return calculateItemUsdPrice(totalAmount, exchangeRate);
    }

    default BigDecimal calculateServiceFee(BigDecimal price, Integer quantity) {
        return price.multiply(BigDecimal.valueOf(quantity)).multiply(getTenantServiceFeeRate()).setScale(2, RoundingMode.HALF_UP);
    }

    default BigDecimal calculateServiceFeeUsd(BigDecimal price, Integer quantity, BigDecimal exchangeRate) {
        return calculateItemUsdPrice(calculateServiceFee(price, quantity), exchangeRate);
    }

    /**
     * 构建价格信息
     */
    default UserOrderDetailVO.PriceInfo buildPriceInfo(TzOrderPurchase purchaseOrder) {
        if (purchaseOrder.getPurchaseOrderNo() != null) {
            UserOrderDetailVO.PriceInfo priceInfo = new UserOrderDetailVO.PriceInfo();
            priceInfo.setSubtotal(purchaseOrder.getCustomerGoodsAmount() != null ? purchaseOrder.getCustomerGoodsAmount() : BigDecimal.ZERO);
            priceInfo.setSubtotalUsd(calculateItemUsdPrice(priceInfo.getSubtotal(), purchaseOrder.getExchangeRateSnapshot()));
            priceInfo.setShippingFee(purchaseOrder.getCustomerTotalFreight() != null ? purchaseOrder.getCustomerTotalFreight() : BigDecimal.ZERO);
            priceInfo.setShippingFeeUsd(calculateItemUsdPrice(priceInfo.getShippingFee(), purchaseOrder.getExchangeRateSnapshot()));
            priceInfo.setServiceFee(purchaseOrder.getServiceFee() != null ? purchaseOrder.getServiceFee() : BigDecimal.ZERO);
            priceInfo.setServiceFeeUsd(calculateItemUsdPrice(priceInfo.getServiceFee(), purchaseOrder.getExchangeRateSnapshot()));
            priceInfo.setTotalAmount(purchaseOrder.getCustomerTotalAmount() != null ? purchaseOrder.getCustomerTotalAmount() : BigDecimal.ZERO);
            priceInfo.setTotalAmountUsd(calculateItemUsdPrice(priceInfo.getTotalAmount(), purchaseOrder.getExchangeRateSnapshot()));
            priceInfo.setPaidAmount(purchaseOrder.getActualPaymentAmount() != null ? purchaseOrder.getActualPaymentAmount() : BigDecimal.ZERO);
            priceInfo.setPaidAmountUsd(calculateItemUsdPrice(priceInfo.getPaidAmount(), purchaseOrder.getExchangeRateSnapshot()));
            priceInfo.setServiceFeeRate(getTenantServiceFeeRate());

            return priceInfo;
        } else {
            return null;
        }
    }

    /**
     * 构建收货地址信息
     */
    default UserOrderDetailVO.DeliveryInfo buildDeliveryInfo(TzOrderPurchase purchaseOrder) {
        if (purchaseOrder.getPurchaseOrderNo() != null) {
            UserOrderDetailVO.DeliveryInfo deliveryInfo = new UserOrderDetailVO.DeliveryInfo();
            deliveryInfo.setReceiverName(purchaseOrder.getConsigneeName());
            deliveryInfo.setReceiverPhone(purchaseOrder.getConsigneePhone());
            deliveryInfo.setAddress(purchaseOrder.getDeliveryAddress());
            deliveryInfo.setCountry(purchaseOrder.getCountryCode());
            deliveryInfo.setProvince(purchaseOrder.getProvince());
            deliveryInfo.setCity(purchaseOrder.getCity());
            deliveryInfo.setZipCode(purchaseOrder.getPostalCode());

            return deliveryInfo;
        } else {
            return null;
        }
    }

    /**
     * 构建操作按钮状态
     */
    default UserOrderDetailVO.ActionButtons buildActionButtons(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            UserOrderDetailVO.ActionButtons actionButtons = new UserOrderDetailVO.ActionButtons();
            actionButtons.setCanCancel(canCancelOrder(status));
            actionButtons.setCanConfirmReceipt(canConfirmReceipt(status));
            actionButtons.setCanApplyRefund(canApplyRefund(status));
            actionButtons.setCanViewTracking(canViewTracking(status));
            actionButtons.setCanReorder(true); // 默认可以重新购买

            return actionButtons;
        } else {
            return null;
        }
    }

    /**
     * 构建订单时间线
     */
    default List<UserOrderDetailVO.OrderTimeline> buildOrderTimeline(TzOrderPurchase purchaseOrder) {
        if (purchaseOrder == null) {
            return Collections.emptyList();
        }

        List<UserOrderDetailVO.OrderTimeline> timeline = new ArrayList<>();
        TzOrderPurchaseStatusEnum currentStatus = purchaseOrder.getOrderStatus();

        // 订单创建
        UserOrderDetailVO.OrderTimeline createTimeline = new UserOrderDetailVO.OrderTimeline();
        createTimeline.setStatusName("订单创建");
        createTimeline.setDescription("订单已创建，等待支付");
        createTimeline.setTime(purchaseOrder.getGmtCreated());
        createTimeline.setCompleted(true);
        createTimeline.setCurrent(false);
        timeline.add(createTimeline);

        // 支付完成
        if (purchaseOrder.getPaidDate() != null) {
            UserOrderDetailVO.OrderTimeline payTimeline = new UserOrderDetailVO.OrderTimeline();
            payTimeline.setStatusName("支付完成");
            payTimeline.setDescription("订单支付已完成");
            payTimeline.setTime(purchaseOrder.getPaidDate());
            payTimeline.setCompleted(true);
            payTimeline.setCurrent(false);
            timeline.add(payTimeline);
        }

        // 订单完成
        if (purchaseOrder.getOrderCompletedDate() != null) {
            UserOrderDetailVO.OrderTimeline completeTimeline = new UserOrderDetailVO.OrderTimeline();
            completeTimeline.setStatusName("订单完成");
            completeTimeline.setDescription("订单已完成");
            completeTimeline.setTime(purchaseOrder.getOrderCompletedDate());
            completeTimeline.setCompleted(true);
            completeTimeline.setCurrent(false);
            timeline.add(completeTimeline);
        }

        // 设置当前状态
        if (!timeline.isEmpty()) {
            timeline.get(timeline.size() - 1).setCurrent(true);
        }

        return timeline;
    }

    /**
     * 获取租户服务费率
     *
     * @return 服务费率（小数形式，如 0.15 表示 15%）
     */
    default BigDecimal getTenantServiceFeeRate() {
        try {
            return EnhancedTenantContextHolder.getEnhancedTenantContext()
              .map(EnhancedTenantContext::getDetailInfo)
              .map(EnhancedTenantContext.TenantDetailInfo::getServiceFee)
              // 数据库中存储的是百分比整数，如 15 表示 15%
              .map(
                serviceFee -> new BigDecimal(serviceFee).divide(new BigDecimal("100"), RoundingMode.HALF_UP)
            ).orElse(new BigDecimal("0.15"));
        } catch (Exception e) {
            // 记录警告日志，但不抛出异常
            System.err.println("获取租户服务费配置失败，使用默认值: " + e.getMessage());
        }
        // 默认服务费率 15%
        return new BigDecimal("0.15");
    }

    /**
     * 转换SKU规格为字符串
     */
    default String convertSkuSpecsToString(List<AttrJson> skuSpecs) {
        if (skuSpecs == null || skuSpecs.isEmpty()) {
            return "";
        }
        return skuSpecs.stream()
            .map(attr -> attr.getAttrKey() + ":" + attr.getAttrValue())
            .collect(Collectors.joining(", "));
    }

    /**
     * 转换SKU规格为英文字符串
     */
    default String convertSkuSpecsToStringEn(List<AttrJson> skuSpecs) {
        if (skuSpecs == null || skuSpecs.isEmpty()) {
            return "";
        }
        return skuSpecs.stream()
            .map(attr -> attr.getAttrKeyTrans() + ":" + attr.getAttrValueTrans())
            .collect(Collectors.joining(", "));
    }
}
