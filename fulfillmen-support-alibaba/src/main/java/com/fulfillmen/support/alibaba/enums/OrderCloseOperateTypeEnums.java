/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * 关闭订单的操作类型
 * <pre>
 * 关闭订单操作类型。CLOSE_TRADE_BY_SELLER:卖家关闭交易,CLOSE_TRADE_BY_BOPS:BOPS后台关闭交易,CLOSE_TRADE_BY_SYSTEM:系统（超时）关闭交易,CLOSE_TRADE_BY_BUYER:买家关闭交易,CLOSE_TRADE_BY_CREADIT:诚信保障投诉关闭
 * </pre>
 * 
 * <AUTHOR>
 * @date 2025/7/28 14:33
 * @description: 订单关闭操作类型
 * @since 1.0.0
 */
@Getter
public enum OrderCloseOperateTypeEnums {

    /**
     * 卖家关闭交易
     */
    CLOSE_TRADE_BY_SELLER("CLOSE_TRADE_BY_SELLER", "卖家关闭交易"),
    /**
     * BOPS后台关闭交易
     */
    CLOSE_TRADE_BY_BOPS("CLOSE_TRADE_BY_BOPS", "BOPS后台关闭交易"),
    /**
     * 系统（超时）关闭交易
     */
    CLOSE_TRADE_BY_SYSTEM("CLOSE_TRADE_BY_SYSTEM", "系统（超时）关闭交易"),
    /**
     * 买家关闭交易
     */
    CLOSE_TRADE_BY_BUYER("CLOSE_TRADE_BY_BUYER", "买家关闭交易"),
    /**
     * 诚信保障投诉关闭
     */
    CLOSE_TRADE_BY_CREADIT("CLOSE_TRADE_BY_CREADIT", "诚信保障投诉关闭");

    private final String code;
    private final String desc;

    OrderCloseOperateTypeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
