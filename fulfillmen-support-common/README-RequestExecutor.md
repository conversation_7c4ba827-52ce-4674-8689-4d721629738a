# 通用请求执行工具类

## 概述

通用请求执行工具类提供了统一的API请求执行抽象层，支持函数式编程风格和响应式编程模式。通过策略模式和接口设计，可以轻松扩展支持不同平台的特定需求。

## 核心组件

### 1. CommonRequestExecutor
通用请求执行器，提供统一的 `executeRequest` 方法。

### 2. RequestExecutionContext
请求执行上下文接口，定义了平台特定的操作：
- 参数构建
- 日志记录
- 异常处理

### 3. AbstractRequestExecutionContext
抽象实现类，提供通用的日志记录和异常处理逻辑。

### 4. RequestParameterBuilder
请求参数构建器接口，支持不同的参数格式。

## 主要特性

- ✅ 支持 `BaseRequest` 和 `BaseRequestRecord` 两种请求类型
- ✅ 统一的参数校验、日志记录和异常处理
- ✅ 支持表单和JSON两种请求格式
- ✅ 响应式编程支持（Mono<T>）
- ✅ 函数式编程风格
- ✅ 灵活的扩展机制
- ✅ 向后兼容性

## 使用方法

### 基本用法

```java
// 1. 创建请求对象（实现 BaseRequestRecord 接口）
public record MyRequest(String param1, String param2) implements BaseRequestRecord {
    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("param1", param1);
        params.put("param2", param2);
        return params;
    }

    @Override
    public void requireParams() {
        assertNotBlank(param1, "param1不能为空");
        assertNotBlank(param2, "param2不能为空");
    }
}

// 2. 创建请求执行上下文
RequestExecutionContext context = new DefaultRequestExecutionContext("MyService", "myOperation");

// 3. 定义API调用函数
Function<MultiValueMap<String, String>, Mono<MyResponse>> apiCall = params -> {
    // 实际的API调用逻辑（如WebClient调用）
    return webClient.post()
        .uri("/api/my-endpoint")
        .body(BodyInserters.fromFormData(params))
        .retrieve()
        .bodyToMono(MyResponse.class);
};

// 4. 执行请求
Mono<MyResponse> result = CommonRequestExecutor.executeRequest(
    new MyRequest("value1", "value2"),
    "/api/my-endpoint",
    apiCall,
    context
);
```

### JSON格式请求

```java
Mono<MyResponse> result = CommonRequestExecutor.executeRequest(
    request,
    "/api/json-endpoint",
    apiCall,
    context,
    true,    // isJson = true
    "data"   // jsonKey = "data"
);
```

## 扩展指南

### 为特定平台创建自定义上下文

```java
public class AlibabaRequestExecutionContext extends AbstractRequestExecutionContext {
    
    private final String appKey;
    private final String secretKey;
    private final String accessToken;
    
    public AlibabaRequestExecutionContext(String serviceName, String operationName,
                                        String appKey, String secretKey, String accessToken) {
        super(serviceName, operationName);
        this.appKey = appKey;
        this.secretKey = secretKey;
        this.accessToken = accessToken;
    }
    
    @Override
    public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, 
                                                               String apiPath, boolean isJson, String jsonKey) {
        // 使用现有的 AlibabaRequestRecordBuilder 逻辑
        if (isJson) {
            return AlibabaRequestRecordBuilder.buildJsonRequest(appKey, secretKey, accessToken, apiPath, 
                params.get(jsonKey), jsonKey).build();
        } else {
            return AlibabaRequestRecordBuilder.buildFormRequest(appKey, secretKey, accessToken, apiPath, params).build();
        }
    }
}
```

### 在现有服务中使用

```java
@Service
public class MyAlibabaService extends BaseAlibabaServiceImpl {
    
    protected <T, R extends BaseAlibabaRequestRecord> Mono<T> executeRequest(
            String operation, R request, String apiPath, 
            Function<MultiValueMap<String, String>, Mono<T>> apiCall) {
        
        RequestExecutionContext context = new AlibabaRequestExecutionContext(
            getServiceName(), operation, appKey, secretKey, accessToken);
            
        return CommonRequestExecutor.executeRequest(request, apiPath, apiCall, context);
    }
}
```

## 配置

在 `application.yml` 中配置：

```yaml
fulfillmen:
  support:
    request-executor:
      enable-detailed-logging: true
      enable-parameter-validation: true
      enable-exception-wrapping: true
      default-timeout-ms: 30000
      max-retry-count: 3
```

## 异常处理

工具类提供统一的异常处理机制：

- `ApiParameterVerificationException`: 参数校验异常
- `ApiCallException`: API调用异常
- `FulfillmenSdkException`: 通用SDK异常

## 日志记录

提供结构化的日志记录：

```
[ServiceName] OperationName 请求开始: request=... params=...
[ServiceName] OperationName 请求成功: request=... response=...
[ServiceName] OperationName 请求失败: request=... error=...
```

## 迁移指南

### 从 AlibabaRequestRecordBuilder 迁移

1. 保持现有接口不变
2. 内部实现改为使用 CommonRequestExecutor
3. 创建 AlibabaRequestExecutionContext
4. 逐步迁移现有代码

### 为 WMS 模块扩展

1. 创建 WmsRequestExecutionContext
2. 实现 WMS 特定的认证和参数处理逻辑
3. 在 WMS 服务中使用新的执行器

## 最佳实践

1. **请求对象设计**: 使用 record 类型实现 BaseRequestRecord 接口
2. **参数校验**: 在 requireParams() 方法中进行完整的参数校验
3. **异常处理**: 让工具类处理异常包装，业务代码专注于业务逻辑
4. **日志记录**: 使用统一的日志格式，便于监控和调试
5. **扩展性**: 通过实现 RequestExecutionContext 接口来扩展平台特定功能

## 示例项目

参考 `RequestExecutorExample.java` 获取完整的使用示例。
