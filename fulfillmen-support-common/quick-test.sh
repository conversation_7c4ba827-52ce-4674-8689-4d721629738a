#!/bin/bash

# 快速测试脚本 - 用于快速验证单个测试类

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "快速测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 <TestClassName>    运行指定的测试类"
    echo "  $0 list              列出所有可用的测试类"
    echo "  $0 help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 CommonRequestExecutorTest"
    echo "  $0 RequestExecutorTestSuite"
    echo ""
}

# 列出所有测试类
list_test_classes() {
    print_message $BLUE "📋 可用的测试类:"
    echo ""
    
    if [ -d "src/test/java" ]; then
        find src/test/java -name "*Test.java" -o -name "*Tests.java" -o -name "*TestSuite.java" | while read -r file; do
            class_name=$(basename "$file" .java)
            relative_path=${file#src/test/java/}
            package_path=${relative_path%/*}
            package_name=${package_path//\//.}
            
            print_message $GREEN "  ✅ $class_name"
            print_message $YELLOW "     包路径: $package_name"
            echo ""
        done
    else
        print_message $RED "❌ 测试目录不存在"
        exit 1
    fi
}

# 运行指定的测试类
run_test_class() {
    local test_class=$1
    
    if [ -z "$test_class" ]; then
        print_message $RED "错误: 请指定测试类名"
        show_help
        exit 1
    fi
    
    print_message $BLUE "🚀 运行测试类: $test_class"
    print_message $YELLOW "⏳ 正在执行测试..."
    
    # 运行测试
    if mvn test -Dtest="$test_class" -Dspring.profiles.active=test; then
        print_message $GREEN "✅ 测试执行成功!"
    else
        print_message $RED "❌ 测试执行失败!"
        exit 1
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "list")
            list_test_classes
            ;;
        "help"|"")
            show_help
            ;;
        *)
            run_test_class "$1"
            ;;
    esac
}

# 执行主函数
main "$@"
