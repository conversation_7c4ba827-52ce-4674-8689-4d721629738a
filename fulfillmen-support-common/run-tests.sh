#!/bin/bash

# 通用请求执行工具类测试运行脚本
# 
# 使用方法:
#   ./run-tests.sh                    # 运行所有测试
#   ./run-tests.sh unit              # 只运行单元测试
#   ./run-tests.sh integration       # 只运行集成测试
#   ./run-tests.sh coverage          # 运行测试并生成覆盖率报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查Maven是否可用
check_maven() {
    if ! command -v mvn &> /dev/null; then
        print_message $RED "错误: Maven 未安装或不在 PATH 中"
        exit 1
    fi
}

# 运行所有测试
run_all_tests() {
    print_message $BLUE "🚀 运行所有测试..."
    mvn clean test -Ptest -Dspring.profiles.active=test -Dspotless.skip=true -Dmaven.test.failure.ignore=false
}

# 运行单元测试
run_unit_tests() {
    print_message $BLUE "🧪 运行单元测试..."
    mvn clean test -Ptest -Dtest="!**/*IntegrationTest" -Dspring.profiles.active=test -Dspotless.skip=true -Dmaven.test.failure.ignore=false
}

# 运行集成测试
run_integration_tests() {
    print_message $BLUE "🔗 运行集成测试..."
    mvn clean test -Ptest -Dtest="**/*IntegrationTest" -Dspring.profiles.active=test -Dspotless.skip=true -Dmaven.test.failure.ignore=false
}

# 运行测试并生成覆盖率报告
run_with_coverage() {
    print_message $BLUE "📊 运行测试并生成覆盖率报告..."
    mvn clean test jacoco:report -Ptest -Dspring.profiles.active=test -Dspotless.skip=true -Dmaven.test.failure.ignore=false

    if [ -f "target/site/jacoco/index.html" ]; then
        print_message $GREEN "✅ 覆盖率报告已生成: target/site/jacoco/index.html"
        print_message $YELLOW "💡 在浏览器中打开: file://$(pwd)/target/site/jacoco/index.html"
    else
        print_message $YELLOW "⚠️  覆盖率报告未生成，请检查测试是否成功执行"
    fi
}

# 运行测试套件
run_test_suite() {
    print_message $BLUE "📋 运行测试套件..."
    mvn clean test -Ptest -Dtest="RequestExecutorTestSuite" -Dspring.profiles.active=test -Dspotless.skip=true -Dmaven.test.failure.ignore=false
}

# 运行特定测试类
run_specific_test() {
    local test_class=$1
    if [ -z "$test_class" ]; then
        print_message $RED "错误: 请指定测试类名"
        echo "示例: $0 specific CommonRequestExecutorTest"
        exit 1
    fi

    print_message $BLUE "🎯 运行特定测试: $test_class"
    mvn clean test -Ptest -Dtest="$test_class" -Dspring.profiles.active=test -Dspotless.skip=true -Dmaven.test.failure.ignore=false
}

# 验证测试环境
verify_test_environment() {
    print_message $BLUE "🔍 验证测试环境..."

    # 检查Java版本
    java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    print_message $GREEN "Java版本: $java_version"

    # 检查Maven版本
    mvn_version=$(mvn -version 2>&1 | head -n 1)
    print_message $GREEN "Maven版本: $mvn_version"

    # 检查项目结构
    if [ -f "pom.xml" ]; then
        print_message $GREEN "✅ 找到 pom.xml"
    else
        print_message $RED "❌ 未找到 pom.xml"
        exit 1
    fi

    if [ -d "src/test/java" ]; then
        print_message $GREEN "✅ 找到测试目录"
    else
        print_message $RED "❌ 未找到测试目录"
        exit 1
    fi

    # 列出测试类
    test_classes=$(find src/test/java -name "*Test.java" | wc -l)
    print_message $GREEN "✅ 找到 $test_classes 个测试类"

    print_message $GREEN "🎉 测试环境验证完成!"
}

# 显示帮助信息
show_help() {
    echo "通用请求执行工具类测试运行脚本"
    echo ""
    echo "使用方法:"
    echo "  $0                           运行所有测试"
    echo "  $0 unit                      只运行单元测试"
    echo "  $0 integration               只运行集成测试"
    echo "  $0 coverage                  运行测试并生成覆盖率报告"
    echo "  $0 suite                     运行测试套件"
    echo "  $0 specific <TestClassName>  运行特定测试类"
    echo "  $0 verify                    验证测试环境"
    echo "  $0 help                      显示此帮助信息"
    echo ""
    echo "测试类别:"
    echo "  - 核心功能测试: CommonRequestExecutorTest"
    echo "  - 上下文测试: AbstractRequestExecutionContextTest, DefaultRequestExecutionContextTest"
    echo "  - 构建器测试: RequestParameterBuilderTest"
    echo "  - 工具类测试: ExceptionMapperTest, RequestLoggerTest"
    echo "  - 集成测试: RequestExecutorIntegrationTest"
    echo ""
    echo "示例:"
    echo "  $0 specific CommonRequestExecutorTest"
    echo "  $0 coverage"
    echo "  $0 verify"
}

# 主函数
main() {
    check_maven

    case "${1:-all}" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "coverage")
            run_with_coverage
            ;;
        "suite")
            run_test_suite
            ;;
        "specific")
            run_specific_test "$2"
            ;;
        "verify")
            verify_test_environment
            return 0
            ;;
        "help")
            show_help
            return 0
            ;;
        "all"|"")
            run_all_tests
            ;;
        *)
            print_message $RED "错误: 未知参数 '$1'"
            show_help
            exit 1
            ;;
    esac

    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_message $GREEN "✅ 测试执行完成!"
    else
        print_message $RED "❌ 测试执行失败! (退出码: $exit_code)"
        print_message $YELLOW "💡 提示: 运行 '$0 verify' 检查测试环境"
        exit $exit_code
    fi
}

# 执行主函数
main "$@"
