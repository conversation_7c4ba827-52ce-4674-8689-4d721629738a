/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.builder;

import java.util.Map;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

/**
 * JSON请求参数构建器
 * 
 * <p>用于构建JSON格式的请求参数。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class JsonRequestParameterBuilder implements RequestParameterBuilder {

    @Override
    public MultiValueMap<String, String> buildParameters(Map<String, String> params, String apiPath) {
        MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
        if (params != null) {
            params.forEach(formParams::add);
        }
        return formParams;
    }

    @Override
    public boolean supports(boolean isJson, String jsonKey) {
        return isJson && StringUtils.hasText(json<PERSON><PERSON>);
    }

}
