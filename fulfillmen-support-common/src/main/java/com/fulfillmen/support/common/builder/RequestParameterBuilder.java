/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.builder;

import java.util.Map;
import org.springframework.util.MultiValueMap;

/**
 * 请求参数构建器接口
 * 
 * <p>定义了将业务参数转换为最终请求参数的标准接口。
 * 不同的实现可以支持不同的参数格式和处理逻辑。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public interface RequestParameterBuilder {

    /**
     * 构建请求参数
     *
     * @param params  业务参数
     * @param apiPath API路径
     * @return 最终的请求参数
     */
    MultiValueMap<String, String> buildParameters(Map<String, String> params, String apiPath);

    /**
     * 是否支持指定的参数格式
     *
     * @param isJson  是否为JSON格式
     * @param jsonKey JSON参数名
     * @return 是否支持
     */
    boolean supports(boolean isJson, String jsonKey);

}
