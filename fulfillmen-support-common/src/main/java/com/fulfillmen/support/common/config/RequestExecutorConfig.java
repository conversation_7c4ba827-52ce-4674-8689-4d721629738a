/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 请求执行器配置类
 * 
 * <p>用于配置请求执行器的各种参数。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@Data
@ConfigurationProperties(prefix = "fulfillmen.support.request-executor")
public class RequestExecutorConfig {

    /**
     * 是否启用详细日志
     */
    private boolean enableDetailedLogging = true;

    /**
     * 是否启用参数校验
     */
    private boolean enableParameterValidation = true;

    /**
     * 是否启用异常包装
     */
    private boolean enableExceptionWrapping = true;

    /**
     * 默认超时时间（毫秒）
     */
    private long defaultTimeoutMs = 30000;

    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

}
