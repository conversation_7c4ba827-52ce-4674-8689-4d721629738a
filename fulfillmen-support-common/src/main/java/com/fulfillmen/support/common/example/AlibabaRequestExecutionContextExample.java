/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.example;

import com.fulfillmen.support.common.executor.AbstractRequestExecutionContext;
import java.util.Map;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

/**
 * Alibaba 请求执行上下文示例
 * 
 * <p>展示如何为 Alibaba 模块扩展通用请求执行工具类。
 * 这个示例展示了如何集成现有的签名和认证逻辑。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class AlibabaRequestExecutionContextExample extends AbstractRequestExecutionContext {

    private final String appKey;
    private final String secretKey;
    private final String accessToken;

    /**
     * 构造函数
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     * @param appKey        应用标识
     * @param secretKey     密钥
     * @param accessToken   访问令牌
     */
    public AlibabaRequestExecutionContextExample(String serviceName, String operationName,
        String appKey, String secretKey, String accessToken) {
        super(serviceName, operationName);
        this.appKey = appKey;
        this.secretKey = secretKey;
        this.accessToken = accessToken;
    }

    @Override
    public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
        try {
            // 这里可以集成现有的 AlibabaRequestRecordBuilder 逻辑
            // 或者重新实现签名和认证逻辑

            // 构建签名参数
            Map<String, String> signParams = buildSignatureParams(params, isJson, jsonKey);

            // 添加时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());
            signParams.put("_aop_timestamp", timestamp);

            // 添加访问令牌
            if (StringUtils.hasText(accessToken)) {
                signParams.put("access_token", accessToken);
            }

            // 生成签名（这里简化处理，实际应该使用 AlibabaSignature.sign）
            String signature = generateSignature(apiPath, signParams);

            // 构建最终请求参数
            MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
            signParams.forEach(formParams::add);
            formParams.add("_aop_signature", signature);

            return formParams;

        } catch (Exception e) {
            throw new RuntimeException("构建Alibaba请求参数失败", e);
        }
    }

    /**
     * 构建签名参数
     */
    private Map<String, String> buildSignatureParams(Map<String, String> params, boolean isJson, String jsonKey) {
        // 实际实现中应该参考 AlibabaRequestRecordBuilder 的逻辑
        return params;
    }

    /**
     * 生成签名
     */
    private String generateSignature(String apiPath, Map<String, String> params) {
        // 实际实现中应该使用 AlibabaSignature.sign 方法
        return "MOCK_SIGNATURE";
    }

    /**
     * 创建工厂方法
     */
    public static AlibabaRequestExecutionContextExample create(String serviceName, String operationName,
        String appKey, String secretKey, String accessToken) {
        return new AlibabaRequestExecutionContextExample(serviceName, operationName, appKey, secretKey, accessToken);
    }

}
