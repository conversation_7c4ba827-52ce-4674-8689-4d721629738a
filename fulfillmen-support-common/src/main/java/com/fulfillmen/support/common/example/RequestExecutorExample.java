/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.example;

import com.fulfillmen.support.common.base.BaseRequestRecord;
import com.fulfillmen.support.common.executor.CommonRequestExecutor;
import com.fulfillmen.support.common.executor.DefaultRequestExecutionContext;
import com.fulfillmen.support.common.executor.RequestExecutionContext;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;

/**
 * 请求执行器使用示例
 * 
 * <p>展示如何使用通用请求执行工具类。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class RequestExecutorExample {

    /**
     * 示例请求记录
     */
    public record ExampleRequest(
        String param1,
        String param2
    ) implements BaseRequestRecord {

        @Override
        public Map<String, String> toParams() {
            Map<String, String> params = new HashMap<>();
            params.put("param1", param1);
            params.put("param2", param2);
            return params;
        }

        @Override
        public void requireParams() {
            assertNotBlank(param1, "param1不能为空");
            assertNotBlank(param2, "param2不能为空");
        }
    }

    /**
     * 示例响应
     */
    public record ExampleResponse(
        String result,
        int code
    ) {
    }

    /**
     * 使用示例
     */
    public void example() {
        // 创建请求对象
        ExampleRequest request = new ExampleRequest("value1", "value2");

        // 创建请求执行上下文
        RequestExecutionContext context = new DefaultRequestExecutionContext("ExampleService", "exampleOperation");

        // 定义API调用函数
        Function<MultiValueMap<String, String>, Mono<ExampleResponse>> apiCall = params -> {
            // 这里是实际的API调用逻辑
            // 例如使用WebClient进行HTTP调用
            return Mono.just(new ExampleResponse("success", 200));
        };

        // 执行请求
        Mono<ExampleResponse> result = CommonRequestExecutor.executeRequest(
            request,
            "/api/example",
            apiCall,
            context
        );

        // 处理结果
        result.subscribe(
            response -> System.out.println("请求成功: " + response),
            error -> System.err.println("请求失败: " + error.getMessage())
        );
    }

    /**
     * JSON格式请求示例
     */
    public void jsonExample() {
        ExampleRequest request = new ExampleRequest("value1", "value2");
        RequestExecutionContext context = new DefaultRequestExecutionContext("ExampleService", "jsonOperation");

        Function<MultiValueMap<String, String>, Mono<ExampleResponse>> apiCall = params -> {
            return Mono.just(new ExampleResponse("json success", 200));
        };

        // 执行JSON格式请求
        Mono<ExampleResponse> result = CommonRequestExecutor.executeRequest(
            request,
            "/api/json-example",
            apiCall,
            context,
            true,  // isJson = true
            "data" // jsonKey = "data"
        );

        result.subscribe(
            response -> System.out.println("JSON请求成功: " + response),
            error -> System.err.println("JSON请求失败: " + error.getMessage())
        );
    }

}
