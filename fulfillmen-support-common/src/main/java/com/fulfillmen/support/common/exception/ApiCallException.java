/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.exception;

import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;

/**
 * API调用异常
 * 
 * <p>当API调用过程中发生错误时抛出此异常。
 * 继承自FulfillmenSdkException，提供更具体的异常类型。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Getter
public class ApiCallException extends FulfillmenSdkException implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 服务名称
     * -- GETTER --
     * 获取服务名称
     *
     */
    private final String serviceName;

    /**
     * 操作名称
     * -- GETTER --
     * 获取操作名称
     *
     */
    private final String operationName;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public ApiCallException(String message) {
        super(message);
        this.serviceName = null;
        this.operationName = null;
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause   原始异常
     */
    public ApiCallException(String message, Throwable cause) {
        super(message, cause);
        this.serviceName = null;
        this.operationName = null;
    }

    /**
     * 构造函数
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     * @param message       错误消息
     */
    public ApiCallException(String serviceName, String operationName, String message) {
        super(String.format("[%s] %s失败: %s", serviceName, operationName, message));
        this.serviceName = serviceName;
        this.operationName = operationName;
    }

    /**
     * 构造函数
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     * @param message       错误消息
     * @param cause         原始异常
     */
    public ApiCallException(String serviceName, String operationName, String message, Throwable cause) {
        super(String.format("[%s] %s失败: %s", serviceName, operationName, message), cause);
        this.serviceName = serviceName;
        this.operationName = operationName;
    }

}
