/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.executor;

import com.fulfillmen.support.common.exception.ApiCallException;
import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.MultiValueMap;

/**
 * 请求执行上下文抽象实现类
 * 
 * <p>提供通用的日志记录和异常处理实现，子类只需要实现特定的参数构建逻辑。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@Slf4j
public abstract class AbstractRequestExecutionContext implements RequestExecutionContext {

    private final String serviceName;
    private final String operationName;

    /**
     * 构造函数
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     */
    protected AbstractRequestExecutionContext(String serviceName, String operationName) {
        this.serviceName = serviceName;
        this.operationName = operationName;
    }

    @Override
    public String getServiceName() {
        return serviceName;
    }

    @Override
    public String getOperationName() {
        return operationName;
    }

    @Override
    public void logRequestStart(Object request, MultiValueMap<String, String> params) {
        log.debug("[{}] {} 请求开始: request={} \n params={}", serviceName, operationName, request, params);
    }

    @Override
    public void logRequestSuccess(Object request, Object response) {
        log.debug("[{}] {} 请求成功: \n request={} \n response={}", serviceName, operationName, request, response);
    }

    @Override
    public void logRequestError(Object request, Throwable error) {
        if (error instanceof ApiParameterVerificationException) {
            log.warn("[{}] {} 请求失败: request={}, error={}", serviceName, operationName, request, error.getMessage());
        } else {
            log.error("[{}] {} 请求失败: request={}, error={}", serviceName, operationName, request, error.getMessage());
        }
    }

    @Override
    public RuntimeException wrapException(Throwable error, String operationName, String serviceName) {
        if (error instanceof ApiParameterVerificationException) {
            return (ApiParameterVerificationException) error;
        }
        return new ApiCallException(serviceName, operationName, error.getMessage(), error);
    }

}
