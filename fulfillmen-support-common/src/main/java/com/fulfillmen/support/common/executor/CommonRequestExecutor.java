/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.executor;

import com.fulfillmen.support.common.base.BaseRequest;
import com.fulfillmen.support.common.base.BaseRequestRecord;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;

/**
 * 通用请求执行器
 * 
 * <p>提供统一的请求执行抽象，支持函数式编程风格和响应式编程模式。
 * 通过 RequestExecutionContext 接口支持不同平台的扩展。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@Slf4j
public class CommonRequestExecutor {

    /**
     * 执行API请求的通用方法（Record版本）
     *
     * @param request 请求对象
     * @param apiPath API路径
     * @param apiCall API调用函数
     * @param context 请求执行上下文
     * @param <T>     响应类型
     * @param <R>     请求类型
     * @return API响应
     */
    public static <T, R extends BaseRequestRecord> Mono<T> executeRequest(
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        RequestExecutionContext context) {
        return executeRequest(request, apiPath, apiCall, context, false, null);
    }

    /**
     * 执行API请求的通用方法（Record版本，支持JSON格式）
     *
     * @param request 请求对象
     * @param apiPath API路径
     * @param apiCall API调用函数
     * @param context 请求执行上下文
     * @param isJson  是否为JSON请求
     * @param jsonKey JSON参数名
     * @param <T>     响应类型
     * @param <R>     请求类型
     * @return API响应
     */
    public static <T, R extends BaseRequestRecord> Mono<T> executeRequest(
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        RequestExecutionContext context,
        boolean isJson,
        String jsonKey) {
        try {
            // 校验请求参数
            request.requireParams();

            // 构建请求参数
            Map<String, String> params;
            if (isJson && jsonKey != null) {
                params = new HashMap<>();
                params.put(jsonKey, request.toJsonString(request));
            } else {
                params = request.toParams();
            }

            // 通过上下文构建最终请求参数
            MultiValueMap<String, String> formParams = context.buildRequestParameters(params, apiPath, isJson, jsonKey);

            return apiCall.apply(formParams)
                .doOnSubscribe(subscription -> context.logRequestStart(request, formParams))
                .doOnSuccess(response -> context.logRequestSuccess(request, response))
                .doOnError(error -> context.logRequestError(request, error))
                .contextCapture()
                .onErrorMap(e -> context.wrapException(e, context.getOperationName(), context.getServiceName()));

        } catch (Exception e) {
            context.logRequestError(request, e);
            return Mono.error(context.wrapException(e, context.getOperationName(), context.getServiceName()));
        }
    }

    /**
     * 执行API请求的通用方法（BaseRequest版本）
     *
     * @param request 请求对象
     * @param apiPath API路径
     * @param apiCall API调用函数
     * @param context 请求执行上下文
     * @param <T>     响应类型
     * @param <R>     请求类型
     * @return API响应
     */
    public static <T, R extends BaseRequest> Mono<T> executeRequest(
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        RequestExecutionContext context) {
        return executeRequest(request, apiPath, apiCall, context, false, null);
    }

    /**
     * 执行API请求的通用方法（BaseRequest版本，支持JSON格式）
     *
     * @param request 请求对象
     * @param apiPath API路径
     * @param apiCall API调用函数
     * @param context 请求执行上下文
     * @param isJson  是否为JSON请求
     * @param jsonKey JSON参数名
     * @param <T>     响应类型
     * @param <R>     请求类型
     * @return API响应
     */
    public static <T, R extends BaseRequest> Mono<T> executeRequest(
        R request,
        String apiPath,
        Function<MultiValueMap<String, String>, Mono<T>> apiCall,
        RequestExecutionContext context,
        boolean isJson,
        String jsonKey) {
        try {
            // 校验请求参数
            request.requireParams();

            // 构建请求参数
            Map<String, String> params;
            if (isJson && jsonKey != null) {
                params = new HashMap<>();
                params.put(jsonKey, request.toJsonString(request));
            } else {
                params = request.toParams();
            }

            // 通过上下文构建最终请求参数
            MultiValueMap<String, String> formParams = context.buildRequestParameters(params, apiPath, isJson, jsonKey);

            return apiCall.apply(formParams)
                .doOnSubscribe(subscription -> context.logRequestStart(request, formParams))
                .doOnSuccess(response -> context.logRequestSuccess(request, response))
                .doOnError(error -> context.logRequestError(request, error))
                .contextCapture()
                .onErrorMap(e -> context.wrapException(e, context.getOperationName(), context.getServiceName()));

        } catch (Exception e) {
            context.logRequestError(request, e);
            return Mono.error(context.wrapException(e, context.getOperationName(), context.getServiceName()));
        }
    }

}
