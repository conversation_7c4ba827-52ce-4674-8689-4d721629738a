/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.executor;

import com.fulfillmen.support.common.builder.FormRequestParameterBuilder;
import com.fulfillmen.support.common.builder.JsonRequestParameterBuilder;
import com.fulfillmen.support.common.builder.RequestParameterBuilder;
import java.util.Map;
import org.springframework.util.MultiValueMap;

/**
 * 默认请求执行上下文实现
 * 
 * <p>提供基本的请求执行上下文实现，适用于简单的API调用场景。
 * 不包含特定平台的认证和签名逻辑。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class DefaultRequestExecutionContext extends AbstractRequestExecutionContext {

    private final RequestParameterBuilder formBuilder = new FormRequestParameterBuilder();
    private final RequestParameterBuilder jsonBuilder = new JsonRequestParameterBuilder();

    /**
     * 构造函数
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     */
    public DefaultRequestExecutionContext(String serviceName, String operationName) {
        super(serviceName, operationName);
    }

    @Override
    public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
        RequestParameterBuilder builder = selectBuilder(isJson, jsonKey);
        return builder.buildParameters(params, apiPath);
    }

    /**
     * 选择合适的参数构建器
     *
     * @param isJson  是否为JSON格式
     * @param jsonKey JSON参数名
     * @return 参数构建器
     */
    private RequestParameterBuilder selectBuilder(boolean isJson, String jsonKey) {
        if (jsonBuilder.supports(isJson, jsonKey)) {
            return jsonBuilder;
        }
        return formBuilder;
    }

}
