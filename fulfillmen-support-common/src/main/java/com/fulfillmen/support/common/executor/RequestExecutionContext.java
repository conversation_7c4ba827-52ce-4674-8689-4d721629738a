/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.executor;

import java.util.Map;
import org.springframework.util.MultiValueMap;

/**
 * 请求执行上下文接口
 * 
 * <p>定义了请求执行过程中需要的各种操作，包括参数构建、日志记录、异常处理等。
 * 不同的平台（如Alibaba、WMS等）可以通过实现此接口来提供特定的实现逻辑。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public interface RequestExecutionContext {

    /**
     * 获取服务名称
     *
     * @return 服务名称
     */
    String getServiceName();

    /**
     * 获取操作名称
     *
     * @return 操作名称
     */
    String getOperationName();

    /**
     * 构建请求参数
     * 
     * <p>将业务参数转换为最终的请求参数，包括添加认证信息、签名等平台特定的处理。</p>
     *
     * @param params  业务参数
     * @param apiPath API路径
     * @param isJson  是否为JSON格式请求
     * @param jsonKey JSON参数名（当isJson为true时使用）
     * @return 最终的请求参数
     */
    MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey);

    /**
     * 记录请求开始日志
     *
     * @param request 请求对象
     * @param params  请求参数
     */
    void logRequestStart(Object request, MultiValueMap<String, String> params);

    /**
     * 记录请求成功日志
     *
     * @param request  请求对象
     * @param response 响应对象
     */
    void logRequestSuccess(Object request, Object response);

    /**
     * 记录请求错误日志
     *
     * @param request 请求对象
     * @param error   异常信息
     */
    void logRequestError(Object request, Throwable error);

    /**
     * 包装异常
     * 
     * <p>将原始异常包装为平台特定的异常类型。</p>
     *
     * @param error         原始异常
     * @param operationName 操作名称
     * @param serviceName   服务名称
     * @return 包装后的异常
     */
    RuntimeException wrapException(Throwable error, String operationName, String serviceName);

}
