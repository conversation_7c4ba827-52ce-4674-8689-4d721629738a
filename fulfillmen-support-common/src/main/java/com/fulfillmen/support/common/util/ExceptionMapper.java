/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.util;

import com.fulfillmen.support.common.exception.ApiCallException;
import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import com.fulfillmen.support.common.exception.FulfillmenSdkException;

/**
 * 异常映射工具类
 * 
 * <p>提供统一的异常包装和映射功能。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class ExceptionMapper {

    /**
     * 包装异常
     *
     * @param error         原始异常
     * @param serviceName   服务名称
     * @param operationName 操作名称
     * @return 包装后的异常
     */
    public static RuntimeException wrapException(Throwable error, String serviceName, String operationName) {
        if (error instanceof ApiParameterVerificationException) {
            return (ApiParameterVerificationException) error;
        }
        if (error instanceof FulfillmenSdkException) {
            return (FulfillmenSdkException) error;
        }
        return new ApiCallException(serviceName, operationName, error.getMessage(), error);
    }

    /**
     * 包装异常（简化版本）
     *
     * @param error   原始异常
     * @param message 错误消息
     * @return 包装后的异常
     */
    public static RuntimeException wrapException(Throwable error, String message) {
        if (error instanceof ApiParameterVerificationException) {
            return (ApiParameterVerificationException) error;
        }
        if (error instanceof FulfillmenSdkException) {
            return (FulfillmenSdkException) error;
        }
        return new ApiCallException(message, error);
    }

    /**
     * 判断是否为参数校验异常
     *
     * @param error 异常
     * @return 是否为参数校验异常
     */
    public static boolean isParameterValidationException(Throwable error) {
        return error instanceof ApiParameterVerificationException;
    }

    /**
     * 判断是否为API调用异常
     *
     * @param error 异常
     * @return 是否为API调用异常
     */
    public static boolean isApiCallException(Throwable error) {
        return error instanceof ApiCallException;
    }

}
