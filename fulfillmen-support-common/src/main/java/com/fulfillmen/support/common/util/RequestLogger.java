/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.util;

import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.MultiValueMap;

/**
 * 请求日志工具类
 * 
 * <p>提供统一的请求日志记录功能。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@Slf4j
public class RequestLogger {

    /**
     * 记录请求开始日志
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     * @param request       请求对象
     * @param params        请求参数
     */
    public static void logRequestStart(String serviceName, String operationName, Object request, MultiValueMap<String, String> params) {
        log.debug("[{}] {} 请求开始: request={} \n params={}", serviceName, operationName, request, params);
    }

    /**
     * 记录请求成功日志
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     * @param request       请求对象
     * @param response      响应对象
     */
    public static void logRequestSuccess(String serviceName, String operationName, Object request, Object response) {
        log.debug("[{}] {} 请求成功: \n request={} \n response={}", serviceName, operationName, request, response);
    }

    /**
     * 记录请求错误日志
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     * @param request       请求对象
     * @param error         异常信息
     */
    public static void logRequestError(String serviceName, String operationName, Object request, Throwable error) {
        if (error == null) {
            log.warn("[{}] {} 请求失败: request={}, error=null", serviceName, operationName, request);
            return;
        }

        if (error instanceof ApiParameterVerificationException) {
            log.warn("[{}] {} 请求失败: request={}, error={}", serviceName, operationName, request, error.getMessage());
        } else {
            log.error("[{}] {} 请求失败: request={}, error={}", serviceName, operationName, request, error.getMessage());
        }
    }

    /**
     * 记录请求异常日志
     *
     * @param serviceName   服务名称
     * @param operationName 操作名称
     * @param request       请求对象
     * @param error         异常信息
     */
    public static void logRequestException(String serviceName, String operationName, Object request, Throwable error) {
        if (error == null) {
            log.warn("[{}] {} 请求异常: request={}, error=null", serviceName, operationName, request);
            return;
        }

        if (error instanceof ApiParameterVerificationException) {
            log.warn("[{}] {} 请求异常: request={}, error={}", serviceName, operationName, request, error.getMessage());
        } else {
            log.error("[{}] {} 请求异常: request={}, error={}", serviceName, operationName, request, error.getMessage(), error);
        }
    }

}
