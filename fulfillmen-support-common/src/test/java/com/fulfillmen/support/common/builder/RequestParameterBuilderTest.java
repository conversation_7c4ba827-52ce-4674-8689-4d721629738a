/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.builder;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.util.MultiValueMap;

/**
 * 请求参数构建器测试类
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class RequestParameterBuilderTest {

    @Test
    @DisplayName("测试表单参数构建器")
    void testFormRequestParameterBuilder() {
        FormRequestParameterBuilder builder = new FormRequestParameterBuilder();

        // 测试支持性检查
        assert builder.supports(false, null);
        assert builder.supports(false, "anyKey");
        assert !builder.supports(true, "jsonKey");

        // 测试参数构建
        Map<String, String> params = new HashMap<>();
        params.put("param1", "value1");
        params.put("param2", "value2");

        MultiValueMap<String, String> result = builder.buildParameters(params, "/api/test");

        assert result.getFirst("param1").equals("value1");
        assert result.getFirst("param2").equals("value2");
        assert result.size() == 2;
    }

    @Test
    @DisplayName("测试表单参数构建器处理空参数")
    void testFormRequestParameterBuilderWithNullParams() {
        FormRequestParameterBuilder builder = new FormRequestParameterBuilder();

        MultiValueMap<String, String> result = builder.buildParameters(null, "/api/test");

        assert result.isEmpty();
    }

    @Test
    @DisplayName("测试JSON参数构建器")
    void testJsonRequestParameterBuilder() {
        JsonRequestParameterBuilder builder = new JsonRequestParameterBuilder();

        // 测试支持性检查
        assert builder.supports(true, "jsonKey");
        assert !builder.supports(true, null);
        assert !builder.supports(true, "");
        assert !builder.supports(false, "jsonKey");

        // 测试参数构建
        Map<String, String> params = new HashMap<>();
        params.put("data", "{\"key\":\"value\"}");

        MultiValueMap<String, String> result = builder.buildParameters(params, "/api/test");

        assert result.getFirst("data").equals("{\"key\":\"value\"}");
        assert result.size() == 1;
    }

    @Test
    @DisplayName("测试JSON参数构建器处理空参数")
    void testJsonRequestParameterBuilderWithNullParams() {
        JsonRequestParameterBuilder builder = new JsonRequestParameterBuilder();

        MultiValueMap<String, String> result = builder.buildParameters(null, "/api/test");

        assert result.isEmpty();
    }

    @Test
    @DisplayName("测试JSON参数构建器的边界条件")
    void testJsonRequestParameterBuilderBoundaryConditions() {
        JsonRequestParameterBuilder builder = new JsonRequestParameterBuilder();

        // 测试各种边界条件
        assert !builder.supports(true, null);
        assert !builder.supports(true, "");
        assert !builder.supports(true, "   ");
        assert !builder.supports(false, "validKey");
        assert builder.supports(true, "validKey");
        assert builder.supports(true, "  validKey  "); // 有文本内容
    }

}
