/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.executor;

import com.fulfillmen.support.common.exception.ApiCallException;
import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * 抽象请求执行上下文测试类
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class AbstractRequestExecutionContextTest {

    private TestAbstractRequestExecutionContext context;

    @BeforeEach
    void setUp() {
        context = new TestAbstractRequestExecutionContext("TestService", "testOperation");
    }

    @Test
    @DisplayName("测试服务名称和操作名称的获取")
    void testServiceAndOperationNames() {
        assert context.getServiceName().equals("TestService");
        assert context.getOperationName().equals("testOperation");
    }

    @Test
    @DisplayName("测试日志记录方法")
    void testLoggingMethods() {
        Object request = new Object();
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("key", "value");
        Object response = new Object();
        Throwable error = new RuntimeException("test error");

        // 测试日志方法不抛出异常
        context.logRequestStart(request, params);
        context.logRequestSuccess(request, response);
        context.logRequestError(request, error);
    }

    @Test
    @DisplayName("测试异常包装 - 参数校验异常")
    void testWrapParameterValidationException() {
        ApiParameterVerificationException originalException = new ApiParameterVerificationException("参数错误");

        RuntimeException wrappedException = context.wrapException(originalException, "testOp", "TestService");

        assert wrappedException instanceof ApiParameterVerificationException;
        assert wrappedException == originalException; // 应该返回原异常
    }

    @Test
    @DisplayName("测试异常包装 - 普通异常")
    void testWrapGeneralException() {
        RuntimeException originalException = new RuntimeException("普通错误");

        RuntimeException wrappedException = context.wrapException(originalException, "testOp", "TestService");

        assert wrappedException instanceof ApiCallException;
        assert wrappedException.getCause() == originalException;
        assert wrappedException.getMessage().contains("TestService");
        assert wrappedException.getMessage().contains("testOp");
    }

    /**
     * 测试用的AbstractRequestExecutionContext实现
     */
    static class TestAbstractRequestExecutionContext extends AbstractRequestExecutionContext {

        public TestAbstractRequestExecutionContext(String serviceName, String operationName) {
            super(serviceName, operationName);
        }

        @Override
        public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
            MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
            if (params != null) {
                params.forEach(formParams::add);
            }
            return formParams;
        }
    }

}
