/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.executor;

import com.fulfillmen.support.common.base.BaseRequest;
import com.fulfillmen.support.common.base.BaseRequestRecord;
import com.fulfillmen.support.common.exception.ApiCallException;
import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import com.fulfillmen.support.common.exception.FulfillmenSdkException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 通用请求执行器测试类
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class CommonRequestExecutorTest {

    /**
     * 测试请求记录（BaseRequestRecord实现）
     */
    record TestRequestRecord(
        String param1,
        String param2
    ) implements BaseRequestRecord {

        @Override
        public Map<String, String> toParams() {
            Map<String, String> params = new HashMap<>();
            params.put("param1", param1);
            params.put("param2", param2);
            return params;
        }

        @Override
        public void requireParams() {
            assertNotBlank(param1, "param1不能为空");
            assertNotBlank(param2, "param2不能为空");
        }
    }

    /**
     * 测试请求类（BaseRequest实现）
     */
    @Data
    @SuperBuilder
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    static class TestRequest extends BaseRequest {

        private String param1;
        private String param2;

        @Override
        public Map<String, String> toParams() {
            Map<String, String> params = new HashMap<>();
            params.put("param1", param1);
            params.put("param2", param2);
            return params;
        }

        @Override
        public void requireParams() throws FulfillmenSdkException {
            assertNotBlank(param1, "param1不能为空");
            assertNotBlank(param2, "param2不能为空");
        }
    }

    /**
     * 测试响应
     */
    record TestResponse(
        String result,
        int code
    ) {
    }

    @Nested
    @DisplayName("核心功能测试")
    class CoreFunctionalityTests {

        @Test
        @DisplayName("测试BaseRequestRecord类型的正常请求执行")
        void testExecuteRequestRecordSuccess() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                // 验证参数传递正确
                assert params.getFirst("param1").equals("value1");
                assert params.getFirst("param2").equals("value2");
                return Mono.just(new TestResponse("success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectNext(new TestResponse("success", 200))
                .verifyComplete();
        }

        @Test
        @DisplayName("测试BaseRequest类型的正常请求执行")
        void testExecuteRequestSuccess() {
            // 准备测试数据
            TestRequest request = TestRequest.builder()
                .param1("value1")
                .param2("value2")
                .build();
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                // 验证参数传递正确
                assert params.getFirst("param1").equals("value1");
                assert params.getFirst("param2").equals("value2");
                return Mono.just(new TestResponse("success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectNext(new TestResponse("success", 200))
                .verifyComplete();
        }

        @Test
        @DisplayName("测试JSON格式请求处理（BaseRequestRecord）")
        void testExecuteRequestRecordWithJsonFormat() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testJsonOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                // 验证JSON参数
                String jsonData = params.getFirst("data");
                assert jsonData != null;
                assert jsonData.contains("value1");
                assert jsonData.contains("value2");
                return Mono.just(new TestResponse("json success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/json-test",
                apiCall,
                context,
                true,  // isJson = true
                "data" // jsonKey = "data"
            );

            // 验证结果
            StepVerifier.create(result)
                .expectNext(new TestResponse("json success", 200))
                .verifyComplete();
        }

        @Test
        @DisplayName("测试JSON格式请求处理（BaseRequest）")
        void testExecuteRequestWithJsonFormat() {
            // 准备测试数据
            TestRequest request = TestRequest.builder()
                .param1("value1")
                .param2("value2")
                .build();
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testJsonOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                // 验证JSON参数
                String jsonData = params.getFirst("payload");
                assert jsonData != null;
                return Mono.just(new TestResponse("json success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/json-test",
                apiCall,
                context,
                true,     // isJson = true
                "payload" // jsonKey = "payload"
            );

            // 验证结果
            StepVerifier.create(result)
                .expectNext(new TestResponse("json success", 200))
                .verifyComplete();
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("测试BaseRequestRecord参数校验失败")
        void testExecuteRequestRecordWithParameterValidationError() {
            // 准备测试数据（参数为空，会触发校验失败）
            TestRequestRecord request = new TestRequestRecord("", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.just(new TestResponse("success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectError(ApiParameterVerificationException.class)
                .verify();
        }

        @Test
        @DisplayName("测试BaseRequest参数校验失败")
        void testExecuteRequestWithParameterValidationError() {
            // 准备测试数据（参数为空，会触发校验失败）
            TestRequest request = TestRequest.builder()
                .param1("")
                .param2("value2")
                .build();
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.just(new TestResponse("success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectError(ApiParameterVerificationException.class)
                .verify();
        }

        @Test
        @DisplayName("测试API调用失败时的异常包装")
        void testExecuteRequestWithApiCallError() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.error(new RuntimeException("API调用失败"));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectError(ApiCallException.class)
                .verify();
        }

        @Test
        @DisplayName("测试网络超时异常的处理")
        void testExecuteRequestWithTimeoutError() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.error(new java.util.concurrent.TimeoutException("请求超时"));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectError(ApiCallException.class)
                .verify();
        }

        @Test
        @DisplayName("测试已包装异常的传播")
        void testExecuteRequestWithWrappedException() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.error(new ApiCallException("TestService", "testOperation", "已包装的异常"));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectError(ApiCallException.class)
                .verify();
        }
    }

    @Nested
    @DisplayName("日志记录测试")
    class LoggingTests {

        private TestRequestExecutionContext testContext;

        @BeforeEach
        void setUp() {
            testContext = new TestRequestExecutionContext("TestService", "testOperation");
        }

        @Test
        @DisplayName("测试请求成功时的日志记录")
        void testLoggingOnSuccess() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.just(new TestResponse("success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                testContext
            );

            // 验证结果和日志
            StepVerifier.create(result)
                .expectNext(new TestResponse("success", 200))
                .verifyComplete();

            // 验证日志调用
            assert testContext.isLogRequestStartCalled();
            assert testContext.isLogRequestSuccessCalled();
            assert !testContext.isLogRequestErrorCalled();
        }

        @Test
        @DisplayName("测试请求失败时的日志记录")
        void testLoggingOnError() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.error(new RuntimeException("API调用失败"));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                testContext
            );

            // 验证结果和日志
            StepVerifier.create(result)
                .expectError(ApiCallException.class)
                .verify();

            // 验证日志调用
            assert testContext.isLogRequestStartCalled();
            assert !testContext.isLogRequestSuccessCalled();
            assert testContext.isLogRequestErrorCalled();
        }

        @Test
        @DisplayName("测试参数校验失败时的日志记录")
        void testLoggingOnParameterValidationError() {
            // 准备测试数据（参数为空，会触发校验失败）
            TestRequestRecord request = new TestRequestRecord("", "value2");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.just(new TestResponse("success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                testContext
            );

            // 验证结果和日志
            StepVerifier.create(result)
                .expectError(ApiParameterVerificationException.class)
                .verify();

            // 验证日志调用（参数校验失败时不会调用logRequestStart）
            assert !testContext.isLogRequestStartCalled();
            assert !testContext.isLogRequestSuccessCalled();
            assert testContext.isLogRequestErrorCalled();
        }

        /**
         * 测试用的RequestExecutionContext实现，用于验证日志调用
         */
        static class TestRequestExecutionContext extends AbstractRequestExecutionContext {

            private boolean logRequestStartCalled = false;
            private boolean logRequestSuccessCalled = false;
            private boolean logRequestErrorCalled = false;

            public TestRequestExecutionContext(String serviceName, String operationName) {
                super(serviceName, operationName);
            }

            @Override
            public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
                MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
                if (params != null) {
                    params.forEach(formParams::add);
                }
                return formParams;
            }

            @Override
            public void logRequestStart(Object request, MultiValueMap<String, String> params) {
                super.logRequestStart(request, params);
                logRequestStartCalled = true;
            }

            @Override
            public void logRequestSuccess(Object request, Object response) {
                super.logRequestSuccess(request, response);
                logRequestSuccessCalled = true;
            }

            @Override
            public void logRequestError(Object request, Throwable error) {
                super.logRequestError(request, error);
                logRequestErrorCalled = true;
            }

            public boolean isLogRequestStartCalled() {
                return logRequestStartCalled;
            }

            public boolean isLogRequestSuccessCalled() {
                return logRequestSuccessCalled;
            }

            public boolean isLogRequestErrorCalled() {
                return logRequestErrorCalled;
            }
        }
    }

    @Nested
    @DisplayName("扩展性测试")
    class ExtensibilityTests {

        @Test
        @DisplayName("测试自定义RequestExecutionContext实现")
        void testCustomRequestExecutionContext() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            CustomRequestExecutionContext context = new CustomRequestExecutionContext("CustomService", "customOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                // 验证自定义参数处理
                assert params.getFirst("custom_prefix_param1").equals("value1");
                assert params.getFirst("custom_prefix_param2").equals("value2");
                assert params.getFirst("custom_timestamp") != null;
                return Mono.just(new TestResponse("custom success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/custom",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectNext(new TestResponse("custom success", 200))
                .verifyComplete();
        }

        @Test
        @DisplayName("测试模拟Alibaba场景的扩展实现")
        void testAlibabaLikeExtension() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            AlibabaLikeRequestExecutionContext context = new AlibabaLikeRequestExecutionContext(
                "AlibabaService", "alibabaOperation", "testAppKey", "testSecretKey", "testAccessToken");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                // 验证Alibaba特有的参数
                assert params.getFirst("_aop_timestamp") != null;
                assert params.getFirst("access_token").equals("testAccessToken");
                assert params.getFirst("_aop_signature") != null;
                return Mono.just(new TestResponse("alibaba success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/alibaba",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectNext(new TestResponse("alibaba success", 200))
                .verifyComplete();
        }

        @Test
        @DisplayName("测试模拟WMS场景的扩展实现")
        void testWmsLikeExtension() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            WmsLikeRequestExecutionContext context = new WmsLikeRequestExecutionContext(
                "WmsService", "wmsOperation", "wmsApiKey", "wmsSecret");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                // 验证WMS特有的参数
                assert params.getFirst("api_key").equals("wmsApiKey");
                assert params.getFirst("wms_timestamp") != null;
                assert params.getFirst("wms_signature") != null;
                return Mono.just(new TestResponse("wms success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/wms",
                apiCall,
                context
            );

            // 验证结果
            StepVerifier.create(result)
                .expectNext(new TestResponse("wms success", 200))
                .verifyComplete();
        }

        /**
         * 自定义RequestExecutionContext实现
         */
        static class CustomRequestExecutionContext extends AbstractRequestExecutionContext {

            public CustomRequestExecutionContext(String serviceName, String operationName) {
                super(serviceName, operationName);
            }

            @Override
            public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
                MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();

                // 添加自定义前缀
                if (params != null) {
                    params.forEach((key, value) -> formParams.add("custom_prefix_" + key, value));
                }

                // 添加自定义时间戳
                formParams.add("custom_timestamp", String.valueOf(System.currentTimeMillis()));

                return formParams;
            }
        }

        /**
         * 模拟Alibaba场景的RequestExecutionContext实现
         */
        static class AlibabaLikeRequestExecutionContext extends AbstractRequestExecutionContext {

            private final String appKey;
            private final String secretKey;
            private final String accessToken;

            public AlibabaLikeRequestExecutionContext(String serviceName, String operationName,
                String appKey, String secretKey, String accessToken) {
                super(serviceName, operationName);
                this.appKey = appKey;
                this.secretKey = secretKey;
                this.accessToken = accessToken;
            }

            @Override
            public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
                MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();

                // 添加业务参数
                if (params != null) {
                    params.forEach(formParams::add);
                }

                // 添加Alibaba特有参数
                formParams.add("_aop_timestamp", String.valueOf(System.currentTimeMillis()));
                formParams.add("access_token", accessToken);
                formParams.add("_aop_signature", "MOCK_ALIBABA_SIGNATURE");

                return formParams;
            }
        }

        /**
         * 模拟WMS场景的RequestExecutionContext实现
         */
        static class WmsLikeRequestExecutionContext extends AbstractRequestExecutionContext {

            private final String apiKey;
            private final String secret;

            public WmsLikeRequestExecutionContext(String serviceName, String operationName,
                String apiKey, String secret) {
                super(serviceName, operationName);
                this.apiKey = apiKey;
                this.secret = secret;
            }

            @Override
            public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
                MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();

                // 添加业务参数
                if (params != null) {
                    params.forEach(formParams::add);
                }

                // 添加WMS特有参数
                formParams.add("api_key", apiKey);
                formParams.add("wms_timestamp", String.valueOf(System.currentTimeMillis()));
                formParams.add("wms_signature", "MOCK_WMS_SIGNATURE");

                return formParams;
            }
        }
    }

    @Nested
    @DisplayName("响应式编程测试")
    class ReactiveTests {

        @Test
        @DisplayName("测试Mono链式操作")
        void testMonoChaining() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.just(new TestResponse("success", 200));
            };

            // 执行测试并进行链式操作
            Mono<String> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            )
                .map(response -> response.result() + "_processed")
                .filter(processedResult -> processedResult.contains("success"))
                .defaultIfEmpty("default_value");

            // 验证结果
            StepVerifier.create(result)
                .expectNext("success_processed")
                .verifyComplete();
        }

        @Test
        @DisplayName("测试响应式操作的副作用处理")
        void testReactiveSideEffects() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            AtomicBoolean subscribed = new AtomicBoolean(false);
            AtomicBoolean succeeded = new AtomicBoolean(false);
            AtomicReference<TestResponse> responseRef = new AtomicReference<>();

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.just(new TestResponse("success", 200));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            )
                .doOnSubscribe(subscription -> subscribed.set(true))
                .doOnSuccess(response -> {
                    succeeded.set(true);
                    responseRef.set(response);
                });

            // 验证结果和副作用
            StepVerifier.create(result)
                .expectNext(new TestResponse("success", 200))
                .verifyComplete();

            assert subscribed.get();
            assert succeeded.get();
            assert responseRef.get() != null;
            assert responseRef.get().result().equals("success");
        }

        @Test
        @DisplayName("测试错误处理的响应式操作")
        void testReactiveErrorHandling() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            AtomicBoolean errorHandled = new AtomicBoolean(false);
            AtomicReference<Throwable> errorRef = new AtomicReference<>();

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.error(new RuntimeException("API调用失败"));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            )
                .doOnError(error -> {
                    errorHandled.set(true);
                    errorRef.set(error);
                })
                .onErrorReturn(new TestResponse("fallback", 500));

            // 验证结果和错误处理
            StepVerifier.create(result)
                .expectNext(new TestResponse("fallback", 500))
                .verifyComplete();

            assert errorHandled.get();
            assert errorRef.get() instanceof ApiCallException;
        }

        @Test
        @DisplayName("测试异步执行和背压处理")
        void testAsyncExecutionAndBackpressure() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.just(new TestResponse("async_success", 200))
                    .delayElement(java.time.Duration.ofMillis(100)); // 模拟异步延迟
            };

            // 执行多个并发请求
            Mono<TestResponse> result1 = CommonRequestExecutor.executeRequest(request, "/api/test1", apiCall, context);
            Mono<TestResponse> result2 = CommonRequestExecutor.executeRequest(request, "/api/test2", apiCall, context);
            Mono<TestResponse> result3 = CommonRequestExecutor.executeRequest(request, "/api/test3", apiCall, context);

            // 合并结果
            Mono<String> combinedResult = Mono.zip(result1, result2, result3)
                .map(tuple -> tuple.getT1().result() + "_" + tuple.getT2().result() + "_" + tuple.getT3().result());

            // 验证结果
            StepVerifier.create(combinedResult)
                .expectNext("async_success_async_success_async_success")
                .verifyComplete();
        }

        @Test
        @DisplayName("测试上下文传播")
        void testContextPropagation() {
            // 准备测试数据
            TestRequestRecord request = new TestRequestRecord("value1", "value2");
            RequestExecutionContext context = new DefaultRequestExecutionContext("TestService", "testOperation");

            Function<MultiValueMap<String, String>, Mono<TestResponse>> apiCall = params -> {
                return Mono.just(new TestResponse("context_success", 200))
                    .contextWrite(ctx -> ctx.put("test_key", "test_value"));
            };

            // 执行测试
            Mono<TestResponse> result = CommonRequestExecutor.executeRequest(
                request,
                "/api/test",
                apiCall,
                context
            );

            // 验证上下文传播
            StepVerifier.create(result)
                .expectNext(new TestResponse("context_success", 200))
                .verifyComplete();
        }
    }

}
