/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.executor;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.util.MultiValueMap;

/**
 * 默认请求执行上下文测试类
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class DefaultRequestExecutionContextTest {

    private DefaultRequestExecutionContext context;

    @BeforeEach
    void setUp() {
        context = new DefaultRequestExecutionContext("DefaultService", "defaultOperation");
    }

    @Test
    @DisplayName("测试表单参数构建")
    void testBuildFormParameters() {
        Map<String, String> params = new HashMap<>();
        params.put("param1", "value1");
        params.put("param2", "value2");

        MultiValueMap<String, String> result = context.buildRequestParameters(params, "/api/test", false, null);

        assert result.getFirst("param1").equals("value1");
        assert result.getFirst("param2").equals("value2");
        assert result.size() == 2;
    }

    @Test
    @DisplayName("测试JSON参数构建")
    void testBuildJsonParameters() {
        Map<String, String> params = new HashMap<>();
        params.put("data", "{\"key\":\"value\"}");

        MultiValueMap<String, String> result = context.buildRequestParameters(params, "/api/test", true, "data");

        assert result.getFirst("data").equals("{\"key\":\"value\"}");
        assert result.size() == 1;
    }

    @Test
    @DisplayName("测试空参数处理")
    void testBuildParametersWithNullParams() {
        MultiValueMap<String, String> result = context.buildRequestParameters(null, "/api/test", false, null);

        assert result.isEmpty();
    }

    @Test
    @DisplayName("测试JSON格式但无jsonKey的情况")
    void testBuildJsonParametersWithoutJsonKey() {
        Map<String, String> params = new HashMap<>();
        params.put("param1", "value1");

        // 当isJson=true但jsonKey为null时，应该使用表单构建器
        MultiValueMap<String, String> result = context.buildRequestParameters(params, "/api/test", true, null);

        assert result.getFirst("param1").equals("value1");
        assert result.size() == 1;
    }

    @Test
    @DisplayName("测试JSON格式但jsonKey为空字符串的情况")
    void testBuildJsonParametersWithEmptyJsonKey() {
        Map<String, String> params = new HashMap<>();
        params.put("param1", "value1");

        // 当isJson=true但jsonKey为空字符串时，应该使用表单构建器
        MultiValueMap<String, String> result = context.buildRequestParameters(params, "/api/test", true, "");

        assert result.getFirst("param1").equals("value1");
        assert result.size() == 1;
    }

}
