/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.integration;

import com.fulfillmen.support.common.base.BaseRequestRecord;
import com.fulfillmen.support.common.exception.ApiCallException;
import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import com.fulfillmen.support.common.executor.AbstractRequestExecutionContext;
import com.fulfillmen.support.common.executor.CommonRequestExecutor;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * 请求执行器集成测试类
 * 
 * <p>测试整个请求执行工具链的协作和集成场景。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class RequestExecutorIntegrationTest {

    /**
     * 集成测试请求记录
     */
    record IntegrationTestRequest(
        String userId,
        String action,
        String data
    ) implements BaseRequestRecord {

        @Override
        public Map<String, String> toParams() {
            Map<String, String> params = new HashMap<>();
            params.put("userId", userId);
            params.put("action", action);
            params.put("data", data);
            return params;
        }

        @Override
        public void requireParams() {
            assertNotBlank(userId, "用户ID不能为空");
            assertNotBlank(action, "操作类型不能为空");
            assertNotBlank(data, "数据不能为空");
        }
    }

    /**
     * 集成测试响应
     */
    record IntegrationTestResponse(
        String requestId,
        String status,
        String message,
        long timestamp
    ) {
    }

    @Test
    @DisplayName("完整的成功请求流程集成测试")
    void testCompleteSuccessFlow() {
        // 准备测试数据
        IntegrationTestRequest request = new IntegrationTestRequest("user123", "create", "test data");
        MockApiRequestExecutionContext context = new MockApiRequestExecutionContext("IntegrationService", "createOperation");

        Function<MultiValueMap<String, String>, Mono<IntegrationTestResponse>> apiCall = params -> {
            // 模拟API调用
            return Mono.just(new IntegrationTestResponse(
                "req_" + System.currentTimeMillis(),
                "success",
                "操作成功",
                System.currentTimeMillis()
            )).delayElement(Duration.ofMillis(50)); // 模拟网络延迟
        };

        // 执行测试
        Mono<IntegrationTestResponse> result = CommonRequestExecutor.executeRequest(
            request,
            "/api/integration/create",
            apiCall,
            context
        );

        // 验证结果
        StepVerifier.create(result)
            .expectNextMatches(response -> response.status().equals("success") &&
                response.message().equals("操作成功") &&
                response.requestId().startsWith("req_")
            )
            .verifyComplete();

        // 验证上下文调用
        assert context.getBuildParametersCallCount() == 1;
        assert context.getLogStartCallCount() == 1;
        assert context.getLogSuccessCallCount() == 1;
        assert context.getLogErrorCallCount() == 0;
    }

    @Test
    @DisplayName("参数校验失败的完整流程集成测试")
    void testCompleteParameterValidationFailureFlow() {
        // 准备测试数据（用户ID为空，会触发校验失败）
        IntegrationTestRequest request = new IntegrationTestRequest("", "create", "test data");
        MockApiRequestExecutionContext context = new MockApiRequestExecutionContext("IntegrationService", "createOperation");

        Function<MultiValueMap<String, String>, Mono<IntegrationTestResponse>> apiCall = params -> {
            return Mono.just(new IntegrationTestResponse("req_123", "success", "不应该执行到这里", System.currentTimeMillis()));
        };

        // 执行测试
        Mono<IntegrationTestResponse> result = CommonRequestExecutor.executeRequest(
            request,
            "/api/integration/create",
            apiCall,
            context
        );

        // 验证结果
        StepVerifier.create(result)
            .expectError(ApiParameterVerificationException.class)
            .verify();

        // 验证上下文调用（参数校验失败时不会调用buildParameters和logStart）
        assert context.getBuildParametersCallCount() == 0;
        assert context.getLogStartCallCount() == 0;
        assert context.getLogSuccessCallCount() == 0;
        assert context.getLogErrorCallCount() == 1;
    }

    @Test
    @DisplayName("API调用失败的完整流程集成测试")
    void testCompleteApiCallFailureFlow() {
        // 准备测试数据
        IntegrationTestRequest request = new IntegrationTestRequest("user123", "create", "test data");
        MockApiRequestExecutionContext context = new MockApiRequestExecutionContext("IntegrationService", "createOperation");

        Function<MultiValueMap<String, String>, Mono<IntegrationTestResponse>> apiCall = params -> {
            return Mono.error(new RuntimeException("网络连接失败"));
        };

        // 执行测试
        Mono<IntegrationTestResponse> result = CommonRequestExecutor.executeRequest(
            request,
            "/api/integration/create",
            apiCall,
            context
        );

        // 验证结果
        StepVerifier.create(result)
            .expectError(ApiCallException.class)
            .verify();

        // 验证上下文调用
        assert context.getBuildParametersCallCount() == 1;
        assert context.getLogStartCallCount() == 1;
        assert context.getLogSuccessCallCount() == 0;
        assert context.getLogErrorCallCount() == 1;
    }

    @Test
    @DisplayName("JSON格式请求的完整流程集成测试")
    void testCompleteJsonRequestFlow() {
        // 准备测试数据
        IntegrationTestRequest request = new IntegrationTestRequest("user123", "update", "json data");
        MockApiRequestExecutionContext context = new MockApiRequestExecutionContext("IntegrationService", "updateOperation");

        Function<MultiValueMap<String, String>, Mono<IntegrationTestResponse>> apiCall = params -> {
            // 验证JSON参数
            String jsonData = params.getFirst("payload");
            assert jsonData != null;
            assert jsonData.contains("user123");

            return Mono.just(new IntegrationTestResponse(
                "req_json_" + System.currentTimeMillis(),
                "success",
                "JSON请求成功",
                System.currentTimeMillis()
            ));
        };

        // 执行JSON格式测试
        Mono<IntegrationTestResponse> result = CommonRequestExecutor.executeRequest(
            request,
            "/api/integration/update",
            apiCall,
            context,
            true,      // isJson = true
            "payload"  // jsonKey = "payload"
        );

        // 验证结果
        StepVerifier.create(result)
            .expectNextMatches(response -> response.status().equals("success") &&
                response.message().equals("JSON请求成功") &&
                response.requestId().startsWith("req_json_")
            )
            .verifyComplete();

        // 验证上下文调用
        assert context.getBuildParametersCallCount() == 1;
        assert context.getLogStartCallCount() == 1;
        assert context.getLogSuccessCallCount() == 1;
        assert context.getLogErrorCallCount() == 0;
    }

    @Test
    @DisplayName("并发请求的集成测试")
    void testConcurrentRequestsIntegration() {
        // 准备测试数据
        IntegrationTestRequest request1 = new IntegrationTestRequest("user1", "action1", "data1");
        IntegrationTestRequest request2 = new IntegrationTestRequest("user2", "action2", "data2");
        IntegrationTestRequest request3 = new IntegrationTestRequest("user3", "action3", "data3");

        MockApiRequestExecutionContext context = new MockApiRequestExecutionContext("IntegrationService", "concurrentOperation");

        Function<MultiValueMap<String, String>, Mono<IntegrationTestResponse>> apiCall = params -> {
            String userId = params.getFirst("userId");
            return Mono.just(new IntegrationTestResponse(
                "req_" + userId,
                "success",
                "并发请求成功",
                System.currentTimeMillis()
            )).delayElement(Duration.ofMillis(100)); // 模拟网络延迟
        };

        // 执行并发请求
        Mono<IntegrationTestResponse> result1 = CommonRequestExecutor.executeRequest(request1, "/api/test1", apiCall, context);
        Mono<IntegrationTestResponse> result2 = CommonRequestExecutor.executeRequest(request2, "/api/test2", apiCall, context);
        Mono<IntegrationTestResponse> result3 = CommonRequestExecutor.executeRequest(request3, "/api/test3", apiCall, context);

        // 合并结果
        Mono<String> combinedResult = Mono.zip(result1, result2, result3)
            .map(tuple -> tuple.getT1().requestId() + "_" + tuple.getT2().requestId() + "_" + tuple.getT3().requestId());

        // 验证结果
        StepVerifier.create(combinedResult)
            .expectNext("req_user1_req_user2_req_user3")
            .verifyComplete();

        // 验证上下文调用次数
        assert context.getBuildParametersCallCount() == 3;
        assert context.getLogStartCallCount() == 3;
        assert context.getLogSuccessCallCount() == 3;
        assert context.getLogErrorCallCount() == 0;
    }

    /**
     * 模拟API请求执行上下文，用于集成测试
     */
    static class MockApiRequestExecutionContext extends AbstractRequestExecutionContext {

        private final AtomicInteger buildParametersCallCount = new AtomicInteger(0);
        private final AtomicInteger logStartCallCount = new AtomicInteger(0);
        private final AtomicInteger logSuccessCallCount = new AtomicInteger(0);
        private final AtomicInteger logErrorCallCount = new AtomicInteger(0);

        public MockApiRequestExecutionContext(String serviceName, String operationName) {
            super(serviceName, operationName);
        }

        @Override
        public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
            buildParametersCallCount.incrementAndGet();

            MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
            if (isJson && jsonKey != null) {
                // 模拟JSON参数处理
                formParams.add(jsonKey, toJsonString(params));
            } else if (params != null) {
                params.forEach(formParams::add);
            }

            // 添加模拟的API特有参数
            formParams.add("api_timestamp", String.valueOf(System.currentTimeMillis()));
            formParams.add("api_signature", "MOCK_SIGNATURE");

            return formParams;
        }

        @Override
        public void logRequestStart(Object request, MultiValueMap<String, String> params) {
            super.logRequestStart(request, params);
            logStartCallCount.incrementAndGet();
        }

        @Override
        public void logRequestSuccess(Object request, Object response) {
            super.logRequestSuccess(request, response);
            logSuccessCallCount.incrementAndGet();
        }

        @Override
        public void logRequestError(Object request, Throwable error) {
            super.logRequestError(request, error);
            logErrorCallCount.incrementAndGet();
        }

        private String toJsonString(Object obj) {
            // 简单的JSON序列化模拟
            return "{\"data\":\"" + obj.toString() + "\"}";
        }

        // Getter方法用于验证调用次数
        public int getBuildParametersCallCount() {
            return buildParametersCallCount.get();
        }

        public int getLogStartCallCount() {
            return logStartCallCount.get();
        }

        public int getLogSuccessCallCount() {
            return logSuccessCallCount.get();
        }

        public int getLogErrorCallCount() {
            return logErrorCallCount.get();
        }
    }

}
