/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.util;

import com.fulfillmen.support.common.exception.ApiCallException;
import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import com.fulfillmen.support.common.exception.FulfillmenSdkException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 异常映射工具测试类
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class ExceptionMapperTest {

    @Test
    @DisplayName("测试包装参数校验异常")
    void testWrapParameterValidationException() {
        ApiParameterVerificationException originalException = new ApiParameterVerificationException("参数错误");

        RuntimeException result = ExceptionMapper.wrapException(originalException, "TestService", "testOperation");

        assert result instanceof ApiParameterVerificationException;
        assert result == originalException; // 应该返回原异常
    }

    @Test
    @DisplayName("测试包装SDK异常")
    void testWrapSdkException() {
        FulfillmenSdkException originalException = new FulfillmenSdkException("SDK错误");

        RuntimeException result = ExceptionMapper.wrapException(originalException, "TestService", "testOperation");

        assert result instanceof FulfillmenSdkException;
        assert result == originalException; // 应该返回原异常
    }

    @Test
    @DisplayName("测试包装普通异常")
    void testWrapGeneralException() {
        RuntimeException originalException = new RuntimeException("普通错误");

        RuntimeException result = ExceptionMapper.wrapException(originalException, "TestService", "testOperation");

        assert result instanceof ApiCallException;
        assert result.getCause() == originalException;
        assert result.getMessage().contains("TestService");
        assert result.getMessage().contains("testOperation");
    }

    @Test
    @DisplayName("测试简化版本的异常包装")
    void testWrapExceptionSimplified() {
        RuntimeException originalException = new RuntimeException("普通错误");

        RuntimeException result = ExceptionMapper.wrapException(originalException, "自定义错误消息");

        assert result instanceof ApiCallException;
        assert result.getCause() == originalException;
        assert result.getMessage().equals("自定义错误消息");
    }

    @Test
    @DisplayName("测试简化版本包装参数校验异常")
    void testWrapParameterValidationExceptionSimplified() {
        ApiParameterVerificationException originalException = new ApiParameterVerificationException("参数错误");

        RuntimeException result = ExceptionMapper.wrapException(originalException, "自定义错误消息");

        assert result instanceof ApiParameterVerificationException;
        assert result == originalException; // 应该返回原异常
    }

    @Test
    @DisplayName("测试异常类型判断方法")
    void testExceptionTypeChecking() {
        ApiParameterVerificationException paramException = new ApiParameterVerificationException("参数错误");
        ApiCallException callException = new ApiCallException("调用错误");
        RuntimeException generalException = new RuntimeException("普通错误");

        // 测试参数校验异常判断
        assert ExceptionMapper.isParameterValidationException(paramException);
        assert !ExceptionMapper.isParameterValidationException(callException);
        assert !ExceptionMapper.isParameterValidationException(generalException);

        // 测试API调用异常判断
        assert ExceptionMapper.isApiCallException(callException);
        assert !ExceptionMapper.isApiCallException(paramException);
        assert !ExceptionMapper.isApiCallException(generalException);
    }

    @Test
    @DisplayName("测试null异常的处理")
    void testNullExceptionHandling() {
        // 测试异常类型判断对null的处理
        assert !ExceptionMapper.isParameterValidationException(null);
        assert !ExceptionMapper.isApiCallException(null);
    }

}
