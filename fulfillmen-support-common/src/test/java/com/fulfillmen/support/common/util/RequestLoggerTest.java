/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.common.util;

import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * 请求日志工具测试类
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public class RequestLoggerTest {

    @Test
    @DisplayName("测试请求开始日志记录")
    void testLogRequestStart() {
        Object request = new TestRequest("test");
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("key", "value");

        // 测试方法不抛出异常
        RequestLogger.logRequestStart("TestService", "testOperation", request, params);
    }

    @Test
    @DisplayName("测试请求成功日志记录")
    void testLogRequestSuccess() {
        Object request = new TestRequest("test");
        Object response = new TestResponse("success");

        // 测试方法不抛出异常
        RequestLogger.logRequestSuccess("TestService", "testOperation", request, response);
    }

    @Test
    @DisplayName("测试请求错误日志记录 - 参数校验异常")
    void testLogRequestErrorWithParameterValidationException() {
        Object request = new TestRequest("test");
        Throwable error = new ApiParameterVerificationException("参数错误");

        // 测试方法不抛出异常
        RequestLogger.logRequestError("TestService", "testOperation", request, error);
    }

    @Test
    @DisplayName("测试请求错误日志记录 - 普通异常")
    void testLogRequestErrorWithGeneralException() {
        Object request = new TestRequest("test");
        Throwable error = new RuntimeException("普通错误");

        // 测试方法不抛出异常
        RequestLogger.logRequestError("TestService", "testOperation", request, error);
    }

    @Test
    @DisplayName("测试请求异常日志记录 - 参数校验异常")
    void testLogRequestExceptionWithParameterValidationException() {
        Object request = new TestRequest("test");
        Throwable error = new ApiParameterVerificationException("参数错误");

        // 测试方法不抛出异常
        RequestLogger.logRequestException("TestService", "testOperation", request, error);
    }

    @Test
    @DisplayName("测试请求异常日志记录 - 普通异常")
    void testLogRequestExceptionWithGeneralException() {
        Object request = new TestRequest("test");
        Throwable error = new RuntimeException("普通错误");

        // 测试方法不抛出异常
        RequestLogger.logRequestException("TestService", "testOperation", request, error);
    }

    @Test
    @DisplayName("测试null参数的处理")
    void testLogWithNullParameters() {
        // 测试方法对null参数的处理
        RequestLogger.logRequestStart("TestService", "testOperation", null, null);
        RequestLogger.logRequestSuccess("TestService", "testOperation", null, null);
        RequestLogger.logRequestError("TestService", "testOperation", null, null);
        RequestLogger.logRequestException("TestService", "testOperation", null, null);
    }

    /**
     * 测试请求类
     */
    record TestRequest(String data) {
    }

    /**
     * 测试响应类
     */
    record TestResponse(String result) {
    }

}
