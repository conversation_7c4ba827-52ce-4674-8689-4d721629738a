# 测试环境配置
spring:
  profiles:
    active: test

# 日志配置
logging:
  level:
    com.fulfillmen.support.common: DEBUG
    reactor: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 请求执行器配置
fulfillmen:
  support:
    request-executor:
      enable-detailed-logging: true
      enable-parameter-validation: true
      enable-exception-wrapping: true
      default-timeout-ms: 5000
      max-retry-count: 2
