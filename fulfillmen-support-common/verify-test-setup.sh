#!/bin/bash

# 测试环境验证脚本
# 用于验证测试环境是否正确配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🔍 开始验证测试环境..."

# 1. 检查基本工具
print_message $YELLOW "1. 检查基本工具..."

if command -v java &> /dev/null; then
    java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    print_message $GREEN "  ✅ Java: $java_version"
else
    print_message $RED "  ❌ Java 未安装"
    exit 1
fi

if command -v mvn &> /dev/null; then
    mvn_version=$(mvn -version 2>&1 | head -n 1 | awk '{print $3}')
    print_message $GREEN "  ✅ Maven: $mvn_version"
else
    print_message $RED "  ❌ Maven 未安装"
    exit 1
fi

# 2. 检查项目结构
print_message $YELLOW "2. 检查项目结构..."

if [ -f "pom.xml" ]; then
    print_message $GREEN "  ✅ pom.xml 存在"
else
    print_message $RED "  ❌ pom.xml 不存在"
    exit 1
fi

if [ -d "src/main/java" ]; then
    print_message $GREEN "  ✅ 主代码目录存在"
else
    print_message $RED "  ❌ 主代码目录不存在"
    exit 1
fi

if [ -d "src/test/java" ]; then
    print_message $GREEN "  ✅ 测试代码目录存在"
else
    print_message $RED "  ❌ 测试代码目录不存在"
    exit 1
fi

# 3. 检查测试类
print_message $YELLOW "3. 检查测试类..."

test_classes=(
    "src/test/java/com/fulfillmen/support/common/executor/CommonRequestExecutorTest.java"
    "src/test/java/com/fulfillmen/support/common/executor/AbstractRequestExecutionContextTest.java"
    "src/test/java/com/fulfillmen/support/common/executor/DefaultRequestExecutionContextTest.java"
    "src/test/java/com/fulfillmen/support/common/builder/RequestParameterBuilderTest.java"
    "src/test/java/com/fulfillmen/support/common/util/ExceptionMapperTest.java"
    "src/test/java/com/fulfillmen/support/common/util/RequestLoggerTest.java"
    "src/test/java/com/fulfillmen/support/common/integration/RequestExecutorIntegrationTest.java"
)

for test_class in "${test_classes[@]}"; do
    if [ -f "$test_class" ]; then
        class_name=$(basename "$test_class" .java)
        print_message $GREEN "  ✅ $class_name"
    else
        class_name=$(basename "$test_class" .java)
        print_message $RED "  ❌ $class_name 不存在"
    fi
done

# 4. 检查依赖
print_message $YELLOW "4. 检查Maven依赖..."

print_message $BLUE "  正在解析依赖..."
if mvn dependency:resolve -q > /dev/null 2>&1; then
    print_message $GREEN "  ✅ Maven依赖解析成功"
else
    print_message $RED "  ❌ Maven依赖解析失败"
    print_message $YELLOW "  💡 尝试运行: mvn dependency:resolve"
    exit 1
fi

# 5. 编译检查
print_message $YELLOW "5. 检查编译..."

print_message $BLUE "  正在编译测试代码..."
if mvn test-compile -q > /dev/null 2>&1; then
    print_message $GREEN "  ✅ 测试代码编译成功"
else
    print_message $RED "  ❌ 测试代码编译失败"
    print_message $YELLOW "  💡 尝试运行: mvn test-compile"
    exit 1
fi

# 6. 运行一个简单测试
print_message $YELLOW "6. 运行简单测试验证..."

print_message $BLUE "  正在运行测试套件..."
if mvn test -Dtest="RequestExecutorTestSuite" -q > /dev/null 2>&1; then
    print_message $GREEN "  ✅ 测试套件运行成功"
else
    print_message $YELLOW "  ⚠️  测试套件运行有问题，但这可能是正常的"
    print_message $YELLOW "  💡 尝试运行: mvn test -Dtest=RequestExecutorTestSuite"
fi

# 7. 检查测试配置
print_message $YELLOW "7. 检查测试配置..."

if [ -f "src/test/resources/application-test.yml" ]; then
    print_message $GREEN "  ✅ 测试配置文件存在"
else
    print_message $YELLOW "  ⚠️  测试配置文件不存在（可选）"
fi

# 8. 总结
print_message $BLUE "📋 验证总结:"
print_message $GREEN "✅ 基本环境检查完成"
print_message $GREEN "✅ 项目结构正确"
print_message $GREEN "✅ 测试类存在"
print_message $GREEN "✅ 依赖解析成功"
print_message $GREEN "✅ 编译检查通过"

print_message $BLUE "🎉 测试环境验证完成！"
print_message $YELLOW "💡 现在可以运行测试了:"
print_message $YELLOW "   ./run-tests.sh                    # 运行所有测试"
print_message $YELLOW "   ./run-tests.sh unit               # 运行单元测试"
print_message $YELLOW "   ./run-tests.sh integration        # 运行集成测试"
print_message $YELLOW "   ./run-tests.sh coverage           # 生成覆盖率报告"
