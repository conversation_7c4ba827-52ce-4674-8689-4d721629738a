# WMS API 与通用请求执行工具类集成指南

## 🎯 集成概述

本指南展示了如何将通用请求执行工具类（CommonRequestExecutor）与现有的 WMS API 声明式接口架构进行集成，实现统一的日志记录、异常处理和参数校验功能。

## 🏗️ 架构设计

### 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Manager Layer (业务层)                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ WmsAccountManager│  │ WmsProductManager│  │ Other Manager│ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Enhanced API Client Layer                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           EnhancedWmsApiClient                          │ │
│  │  ┌─────────────────┐  ┌─────────────────────────────┐  │ │
│  │  │CommonRequestExecutor│  │WmsRequestExecutionContext│  │ │
│  │  └─────────────────┘  └─────────────────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│              Declarative HTTP Interface Layer               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                WmsFulfillmenAPI                         │ │
│  │  (@HttpExchange, @GetExchange, @PostExchange)          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    WebClient Layer                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              HttpServiceProxyFactory                    │ │
│  │                    WebClient                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **WmsRequestExecutionContext**: WMS 特定的请求执行上下文
2. **EnhancedWmsApiClient**: 增强的 API 客户端，集成通用请求执行工具类
3. **WmsBaseRequestRecord**: WMS 基础请求记录接口
4. **Manager 层**: 业务逻辑层，使用增强的 API 客户端

## 🚀 使用示例

### 1. 基本使用 - Manager 层调用

```java
@Service
@RequiredArgsConstructor
public class WmsAccountManager {
    
    private final EnhancedWmsApiClient enhancedWmsApiClient;
    private final WmsFulfillmenAPI wmsFulfillmenAPI;
    
    public Mono<WmsAccountInfo> getAccountInfoByAuthCode(String authCode) {
        // 创建请求记录
        WmsAccountInfoRequestRecord request = WmsAccountInfoRequestRecord.byAuthCode(authCode);
        
        // 使用增强的API客户端执行请求
        return enhancedWmsApiClient.executeRequest(
            request,
            enhancedWmsApiClient.adaptDeclarativeApi(wmsFulfillmenAPI::getAccountInfo)
        );
    }
}
```

### 2. 自定义请求记录

```java
public record WmsAccountInfoRequestRecord(
    String authCode,
    String customerCode,
    QueryType queryType
) implements WmsBaseRequestRecord {
    
    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        // 添加业务参数
        if (authCode != null) {
            params.put("Code", authCode);
        }
        // 添加通用WMS参数
        addCommonWmsParams(params);
        return params;
    }
    
    @Override
    public void requireParams() {
        assertNotBlank(authCode, "授权码不能为空");
    }
}
```

### 3. 批量请求处理

```java
public Mono<List<WmsAccountInfo>> getAccountInfoBatch(List<String> authCodes) {
    // 并行执行多个请求
    List<Mono<WmsAccountInfo>> requests = authCodes.stream()
        .map(this::getAccountInfoByAuthCode)
        .collect(Collectors.toList());
    
    // 合并所有结果
    return Mono.zip(requests, results -> {
        return Arrays.stream(results)
            .map(result -> (WmsAccountInfo) result)
            .collect(Collectors.toList());
    });
}
```

## 🔧 配置要求

### 1. Maven 依赖

确保在 `fulfillmen-support-wms` 项目的 `pom.xml` 中添加对通用工具类的依赖：

```xml
<dependency>
    <groupId>com.fulfillmen.support</groupId>
    <artifactId>fulfillmen-support-common</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 2. Spring 配置

```java
@Configuration
@EnableConfigurationProperties(WmsProperties.class)
public class WmsApiConfiguration {
    
    @Bean
    public EnhancedWmsApiClient enhancedWmsApiClient(
            WmsFulfillmenAPI wmsFulfillmenAPI,
            WmsProperties wmsProperties) {
        return new EnhancedWmsApiClient(wmsFulfillmenAPI, wmsProperties);
    }
}
```

## ✨ 集成优势

### 1. 保持现有架构优势
- ✅ **声明式接口**: 继续使用 `@HttpExchange` 等注解
- ✅ **类型安全**: 编译时类型检查
- ✅ **代码简洁**: 接口定义清晰明了
- ✅ **Spring 集成**: 无缝集成 Spring 生态

### 2. 获得通用工具类功能
- ✅ **统一日志记录**: 标准化的请求日志格式
- ✅ **参数校验**: 自动的请求参数验证
- ✅ **异常处理**: 统一的异常包装和处理
- ✅ **请求追踪**: 完整的请求生命周期追踪
- ✅ **响应式支持**: 完整的 Reactor 集成

### 3. 业务层隔离
- ✅ **Manager 接口**: 业务层不直接调用 API 接口
- ✅ **统一入口**: 通过 EnhancedWmsApiClient 统一管理
- ✅ **可测试性**: 易于进行单元测试和集成测试
- ✅ **可维护性**: 清晰的分层和职责分离

## 📊 功能对比

| 功能特性 | 原有架构 | 集成后架构 |
|---------|---------|-----------|
| 声明式接口 | ✅ | ✅ |
| 类型安全 | ✅ | ✅ |
| 统一日志 | ❌ | ✅ |
| 参数校验 | ❌ | ✅ |
| 异常处理 | 基础 | ✅ 增强 |
| 请求追踪 | ❌ | ✅ |
| 业务层隔离 | 部分 | ✅ 完整 |
| 测试支持 | 基础 | ✅ 增强 |

## 🧪 测试策略

### 1. 单元测试
```java
@Test
void testAccountInfoQuery() {
    WmsAccountInfoRequestRecord request = WmsAccountInfoRequestRecord.byAuthCode("TEST_CODE");
    
    StepVerifier.create(wmsAccountManager.getAccountInfoByAuthCode("TEST_CODE"))
        .expectNextMatches(accountInfo -> accountInfo != null)
        .verifyComplete();
}
```

### 2. 集成测试
```java
@SpringBootTest
@ActiveProfiles("test")
class WmsApiIntegrationTest {
    
    @Autowired
    private WmsAccountManager wmsAccountManager;
    
    @Test
    void testFullIntegration() {
        // 测试完整的集成流程
    }
}
```

## 🔄 迁移指南

### 1. 渐进式迁移
1. **第一阶段**: 创建 EnhancedWmsApiClient，与现有 WmsApiClient 并存
2. **第二阶段**: 逐步迁移 Manager 层使用新的客户端
3. **第三阶段**: 完全替换旧的客户端实现

### 2. 兼容性保证
- 现有的声明式接口定义无需修改
- Manager 层的接口保持不变
- 配置文件向后兼容

## 📈 性能考虑

### 1. 响应式编程
- 使用 Mono/Flux 进行异步处理
- 支持背压和流控制
- 高并发场景下的性能优势

### 2. 批量处理
- 并行执行多个请求
- 合理的超时和重试机制
- 资源使用优化

## 🎉 总结

通过这个集成方案，我们成功地：

1. **保持了现有架构的优势**：声明式接口、类型安全、Spring 集成
2. **获得了通用工具类的功能**：统一日志、参数校验、异常处理
3. **实现了业务层的完全隔离**：Manager 层不直接调用 API 接口
4. **提供了完整的测试支持**：单元测试和集成测试
5. **确保了代码的可维护性**：清晰的分层和职责分离

这个方案为 WMS API 的使用提供了一个强大、灵活且易于维护的基础架构。
