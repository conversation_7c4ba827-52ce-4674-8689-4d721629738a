/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import com.fulfillmen.support.common.executor.CommonRequestExecutor;
import com.fulfillmen.support.wms.autoconfigure.WmsProperties;
import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.common.WmsProduct;
import com.fulfillmen.support.wms.dto.request.DeductAccountReq;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateGoodsReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.DeductAccountRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import com.fulfillmen.support.wms.executor.WmsRequestExecutionContext;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 增强的 WMS API 客户端
 * 
 * <p>集成通用请求执行工具类的 WMS API 客户端，提供以下增强功能：
 * <ul>
 *   <li>统一的请求执行抽象（使用 CommonRequestExecutor）</li>
 *   <li>标准化的日志记录和异常处理</li>
 *   <li>参数校验和请求追踪</li>
 *   <li>与现有声明式接口的无缝集成</li>
 *   <li>支持响应式编程模式</li>
 * </ul>
 * </p>
 * 
 * <p><strong>架构设计：</strong></p>
 * <pre>
 * Manager Layer (业务层)
 *     ↓
 * EnhancedWmsApiClient (增强客户端 - 集成CommonRequestExecutor)
 *     ↓
 * WmsFulfillmenAPI (声明式HTTP接口)
 *     ↓
 * WebClient (HTTP客户端)
 * </pre>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EnhancedWmsApiClient {

    private final WmsFulfillmenAPI wmsFulfillmenAPI;
    private final WmsProperties wmsProperties;

    // ==================== 通用请求执行方法 ====================

    /**
     * 执行 WMS API 请求的通用方法
     * 
     * <p>使用通用请求执行工具类来执行 WMS API 请求，提供统一的：
     * <ul>
     *   <li>参数校验</li>
     *   <li>日志记录</li>
     *   <li>异常处理</li>
     *   <li>请求追踪</li>
     * </ul>
     * </p>
     *
     * @param request     请求对象（必须实现 WmsBaseRequestRecord）
     * @param apiCall     API调用函数（声明式接口方法的引用）
     * @param <T>         响应数据类型
     * @param <R>         请求类型
     * @return API响应的Mono
     */
    public <T, R extends WmsBaseRequestRecord> Mono<T> executeRequest(
            R request,
            Function<MultiValueMap<String, String>, Mono<WmsApiResponse<T>>> apiCall) {
        
        // 创建WMS特定的请求执行上下文
        WmsRequestExecutionContext context = WmsRequestExecutionContext.create(
            request.getOperationType(), wmsProperties);
        
        // 使用通用请求执行工具类执行请求
        return CommonRequestExecutor.executeRequest(
            request,
            "/api/" + request.getOperationType().toLowerCase(),
            params -> apiCall.apply(params).map(this::extractData),
            context
        );
    }

    /**
     * 执行 WMS API 请求的通用方法（支持JSON格式）
     *
     * @param request     请求对象
     * @param apiCall     API调用函数
     * @param jsonKey     JSON参数名
     * @param <T>         响应数据类型
     * @param <R>         请求类型
     * @return API响应的Mono
     */
    public <T, R extends WmsBaseRequestRecord> Mono<T> executeJsonRequest(
            R request,
            Function<MultiValueMap<String, String>, Mono<WmsApiResponse<T>>> apiCall,
            String jsonKey) {
        
        WmsRequestExecutionContext context = WmsRequestExecutionContext.create(
            request.getOperationType(), wmsProperties);
        
        return CommonRequestExecutor.executeRequest(
            request,
            "/api/" + request.getOperationType().toLowerCase(),
            params -> apiCall.apply(params).map(this::extractData),
            context,
            true,    // isJson = true
            jsonKey  // JSON参数名
        );
    }

    /**
     * 执行同步 WMS API 请求
     * 
     * <p>对于需要同步结果的场景，提供阻塞式的API调用方法。</p>
     *
     * @param request     请求对象
     * @param apiCall     API调用函数
     * @param <T>         响应数据类型
     * @param <R>         请求类型
     * @return API响应数据
     */
    public <T, R extends WmsBaseRequestRecord> T executeRequestSync(
            R request,
            Function<MultiValueMap<String, String>, Mono<WmsApiResponse<T>>> apiCall) {
        
        return executeRequest(request, apiCall).block();
    }

    // ==================== 声明式接口适配器方法 ====================

    /**
     * 适配现有的声明式接口调用
     * 
     * <p>将现有的声明式接口方法包装为支持通用请求执行工具类的形式。</p>
     *
     * @param declarativeApiCall 声明式API调用（如 wmsFulfillmenAPI::getAccountInfo）
     * @param <T>                响应数据类型
     * @return 适配后的API调用函数
     */
    public <T> Function<MultiValueMap<String, String>, Mono<WmsApiResponse<T>>> adaptDeclarativeApi(
            Function<String, Mono<WmsApiResponse<T>>> declarativeApiCall) {
        
        return params -> {
            // 从参数中提取主要的业务参数（如customerCode）
            String primaryParam = extractPrimaryParam(params);
            return declarativeApiCall.apply(primaryParam);
        };
    }

    /**
     * 适配带多个参数的声明式接口调用
     *
     * @param declarativeApiCall 声明式API调用
     * @param <T>                响应数据类型
     * @return 适配后的API调用函数
     */
    public <T> Function<MultiValueMap<String, String>, Mono<WmsApiResponse<T>>> adaptDeclarativeApiMultiParam(
            Function<MultiValueMap<String, String>, Mono<WmsApiResponse<T>>> declarativeApiCall) {
        
        return declarativeApiCall;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 从WMS API响应中提取数据
     *
     * @param response WMS API响应
     * @param <T>      数据类型
     * @return 提取的数据
     */
    private <T> T extractData(WmsApiResponse<T> response) {
        if (response == null || !response.isSuccess()) {
            throw new RuntimeException("WMS API调用失败: " + 
                (response != null ? response.getMessage() : "响应为空"));
        }
        return response.getData();
    }

    /**
     * 从参数中提取主要参数
     *
     * @param params 请求参数
     * @return 主要参数值
     */
    private String extractPrimaryParam(MultiValueMap<String, String> params) {
        // 优先级：customerCode > authCode > 第一个参数
        String value = params.getFirst("customerCode");
        if (value == null) {
            value = params.getFirst("authCode");
        }
        if (value == null && !params.isEmpty()) {
            value = params.values().iterator().next().get(0);
        }
        return value;
    }

    // ==================== 便捷方法 ====================

    /**
     * 创建简单的请求记录
     *
     * @param operationType  操作类型
     * @param customerCode   客户码
     * @param additionalData 额外数据
     * @return 请求记录
     */
    public WmsBaseRequestRecord createSimpleRequest(String operationType, String customerCode, 
                                                   Map<String, String> additionalData) {
        return new WmsBaseRequestRecord() {
            @Override
            public String getOperationType() {
                return operationType;
            }

            @Override
            public String getCustomerCode() {
                return customerCode;
            }

            @Override
            public Map<String, String> toParams() {
                Map<String, String> params = new HashMap<>();
                if (customerCode != null) {
                    params.put("customerCode", customerCode);
                }
                if (additionalData != null) {
                    params.putAll(additionalData);
                }
                addCommonWmsParams(params);
                return params;
            }

            @Override
            public void requireParams() {
                if (customerCode != null) {
                    validateCustomerCode(customerCode, "customerCode");
                }
            }
        };
    }
}
