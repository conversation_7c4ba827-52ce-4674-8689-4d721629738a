/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.common.WmsProduct;
import com.fulfillmen.support.wms.dto.request.DeductAccountReq;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateGoodsReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.DeductAccountRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * WMS API客户端
 *
 * <p>
 * 封装WMS API的调用逻辑，使用声明式HTTP接口进行API调用。
 * 提供账户管理、订单管理、产品管理等功能的API调用方法。
 * </p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WmsApiClient {

    private final WmsFulfillmenAPI wmsFulfillmenAPI;

    // ==================== 账户管理相关API ====================

    /**
     * 通过授权码获取账户信息
     *
     * @param authCode 授权码
     * @return 解密后的账户信息
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsAccountInfoRes getAccountInfoByAuthCode(String authCode) {
        try {
            log.debug("调用WMS通过授权码获取账户信息API: authCode=[{}]", authCode);

            return wmsFulfillmenAPI.getAccountInfoByAuthCode(authCode)
                    .<WmsAccountInfoRes>handle((response, sink) -> {
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS获取账户信息API调用失败，响应为空或失败"));
                            return;
                        }

                        try {
                            // 解密用户信息
                            WmsAccountInfoRes data = response.getData();
                            log.debug("用户信息: {}", data);
                            if (Objects.isNull(data)) {
                                log.error("无法获取用户信息为空: {}", authCode);
                                sink.error(new WmsApiException("解密后的用户信息为空"));
                                return;
                            }
                            sink.next(data);
                        } catch (Exception e) {
                            log.error("解密WMS账户信息失败: error=[{}]", e.getMessage(), e);
                            sink.error(new WmsApiException("解密WMS账户信息失败", e));
                        }
                    }).block();
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS获取账户信息API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS获取账户信息API调用失败", e);
        }
    }

    /**
     * 通过客户码获取账户信息
     *
     * @param customerCode 客户码
     * @return 账户信息
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsAccountInfoRes getAccountInfoByCustomerCode(String customerCode) {
        try {
            log.debug("调用WMS通过客户码获取账户信息API: customerCode=[{}]", customerCode);

            return wmsFulfillmenAPI.getAccountInfoByCustomerCode(customerCode)
                    .<WmsAccountInfoRes>handle((response, sink) -> {
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS获取账户信息API调用失败，响应为空或失败"));
                            return;
                        }
                        log.debug("WMS获取账户信息API调用成功");
                        sink.next(response.getData());
                    }).block();
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS获取账户信息API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS获取账户信息API调用失败", e);
        }
    }

    // ==================== 采购订单相关API ====================

    /**
     * 采购商品扣减账户余额
     *
     * @param request 扣减请求
     * @return API响应结果
     * @throws WmsApiException 当API调用失败时抛出
     */
    public DeductAccountRes deductAccountInfoByPurchase(DeductAccountReq request) {
        try {
            log.debug("调用WMS扣减账户余额API: {}", request);

            return wmsFulfillmenAPI.deductAccountInfoByPurchase(request)
                    .<DeductAccountRes>handle((response, sink) -> {
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS扣减账户余额API调用失败，响应为空或失败"));
                            return;
                        }
                        log.debug("WMS扣减账户余额API调用成功");
                        sink.next(response.getData());
                    }).block();
        } catch (Exception e) {
            log.error("WMS扣减账户余额API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS扣减账户余额API调用失败", e);
        }
    }

    /**
     * 创建采购订单
     *
     * @param request 创建订单请求
     * @return 采购数据响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    @Deprecated
    public WmsPurchaseDataRes createPurchaseOrder(WmsCreateOrderReq request) {
        try {
            log.debug("调用WMS创建采购订单API: customerCode=[{}], orderCount=[{}]",
                    request.getCustomerCode(), request.getOrders().size());

            return wmsFulfillmenAPI.createPurchaseOrder(request)
                    .<WmsPurchaseDataRes>handle((response, sink) -> {
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS创建采购订单API调用失败，响应为空或失败"));
                            return;
                        }
                        log.debug("WMS创建采购订单API调用成功");
                        sink.next(response.getData());
                    }).block();
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS创建采购订单API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS创建采购订单API调用失败", e);
        }
    }

    /**
     * 创建采购订单
     *
     * @param request 创建订单请求
     * @return 采购数据响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    public List<WmsCreatePurchaseOrderRes> createPurchaseOrderNew(WmsCreateOrderReq request) {
        try {
            log.debug("调用WMS创建采购订单API: customerCode=[{}], orderCount=[{}]",
                    request.getCustomerCode(), request.getOrders().size());

            return wmsFulfillmenAPI.createPurchaseOrderNew(request)
                    .<List<WmsCreatePurchaseOrderRes>>handle((response, sink) -> {
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS创建采购订单API调用失败，响应为空或失败"));
                            return;
                        }
                        log.debug("WMS创建采购订单API调用成功");
                        sink.next(response.getData());
                    }).block();
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS创建采购订单API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS创建采购订单API调用失败", e);
        }
    }

    /**
     * 更新 采购订单。
     *
     * @param request 创建订单请求
     * @return 采购数据响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    public Void updatePurchaseOrder(List<WmsPurchaseOrderDetailReq> request) {
        try {
            // 拼接多个 purchaseNo .逗号分割
            String purchaseNos = request.stream().map(WmsPurchaseOrderDetailReq::getPurchaseNo)
                    .collect(Collectors.joining(","));
            log.debug("调用 WMS 更新采购订单API: orderCount=[{}] , 更新wms 的 purchaseNos : [{}]", request.size(), purchaseNos);
            return wmsFulfillmenAPI.updatePurchaseOrderDetails(request)
                    .<Void>handle((response, sink) -> {
                        // 请求更新失败
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS创建采购订单API调用失败，响应为空或失败"));
                        }
                    }).block();
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新 WMS 采购订单API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("更新 WMS 采购订单API调用失败", e);
        }
    }

    /**
     * 查询采购订单
     *
     * @param request 订单查询请求
     * @return 分页订单信息
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsPageDTO<WmsPurchaseOrderInfoRes> queryOrder(WmsOrderQueryReq request) {
        try {
            log.debug("调用WMS查询采购订单API: page=[{}], pageSize=[{}]", request.getPage(), request.getPageSize());

            return wmsFulfillmenAPI.queryOrder(request)
                    .<WmsPageDTO<WmsPurchaseOrderInfoRes>>handle((response, sink) -> {
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS查询采购订单API调用失败，响应为空或失败"));
                            return;
                        }
                        log.debug("WMS查询采购订单API调用成功");
                        sink.next(response.getData());
                    }).block();
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS查询采购订单API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS查询采购订单API调用失败", e);
        }
    }

    /**
     * 查询采购订单详情
     *
     * @param request 订单详情查询请求
     * @return 订单详情列表
     * @throws WmsApiException 当API调用失败时抛出
     */
    public List<WmsPurchaseOrderDetailsRes> queryOrderDetail(PurchaseOrderDetailReq request) {
        try {
            log.debug("调用WMS查询采购订单详情API: purchaseNo=[{}], orderId=[{}], wmsPurchaseNo=[{}]",
                    request.getPurchaseNo(), request.getOrderId(), request.getWmsPurchaseNo());

            return wmsFulfillmenAPI.queryOrderDetails(
                    request.getPurchaseNo(),
                    request.getOrderId(),
                    request.getWmsPurchaseNo())
                    .<List<WmsPurchaseOrderDetailsRes>>handle((response, sink) -> {
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS查询采购订单详情API调用失败，响应为空或失败"));
                            return;
                        }
                        log.debug("WMS查询采购订单详情API调用成功");
                        sink.next(response.getData());
                    }).block();
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS查询采购订单详情API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS查询采购订单详情API调用失败", e);
        }
    }

    // ==================== 产品管理相关API ====================

    /**
     * 创建产品
     *
     * @param request 产品创建请求
     * @return API响应结果
     * @throws WmsApiException 当API调用失败时抛出
     */
    public void createProduct(WmsCreateGoodsReq request) {
        try {
            log.debug("调用WMS创建产品API: productName=[{}]", request.getCnName());

            wmsFulfillmenAPI.createProduct(request)
                    .<Object>handle((response, sink) -> {
                        if (response == null || !response.isSuccess()) {
                            sink.error(new WmsApiException("WMS创建产品API调用失败，响应为空或失败"));
                            return;
                        }
                        log.debug("WMS创建产品API调用成功");
                        // sink.next(response.getData());
                    }).block();
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS创建产品API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS创建产品API调用失败", e);
        }
    }

    /**
     * 获取产品列表
     *
     * @param page    页码
     * @param barcode 条码
     * @return 产品列表响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsApiResponse<List<WmsProduct>> getProductList(Integer page, String barcode) {
        try {
            log.debug("调用WMS获取产品列表API: page=[{}], barcode=[{}]", page, barcode);

            WmsApiResponse<List<WmsProduct>> response = wmsFulfillmenAPI
                    .getProductList(WmsProductQueryReq.builder().page(page).barcode(barcode).build());
            log.debug("WMS获取产品列表API调用成功");
            return response;
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS获取产品列表API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS获取产品列表API调用失败", e);
        }
    }

    /**
     * 获取产品列表
     *
     * @param page    页码
     * @param barcode 条码
     * @return 产品列表响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsApiResponse<WmsProduct> getProductInfo(String cusCode, String offerId, String sku) {
        try {
            log.debug("调用WMS获取产品列表API: cusCode=[{}], offerId=[{}], sku=[{}]", cusCode, offerId, sku);

            WmsApiResponse<WmsProduct> response = wmsFulfillmenAPI.getProductInfo(cusCode, offerId, sku).block();
            log.debug("WMS获取产品列表API调用成功");
            return response;
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS获取产品列表API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS获取产品列表API调用失败", e);
        }
    }
}
