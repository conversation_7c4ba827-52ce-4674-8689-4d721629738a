/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.common.WmsProduct;
import com.fulfillmen.support.wms.dto.request.DeductAccountReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateGoodsReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateInboundOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.DeductAccountRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import java.util.List;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * WMS Fulfillmen API 声明式HTTP接口
 *
 * <p>使用Spring 6.x的声明式HTTP接口模式，定义WMS系统的所有API调用。
 * 支持账户管理、订单管理、产品管理等功能模块。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@HttpExchange
public interface WmsFulfillmenAPI {

    // ==================== 请求头常量 ====================

    /**
     * 随机数请求头
     */
    String X_NONCE = "X-Nonce";

    /**
     * 时间戳请求头
     */
    String X_TIMESTAMP = "X-Timestamp";

    /**
     * 签名请求头（预留）
     */
    String X_SIGNATURE = "X-Signature";

    // ==================== 账户管理相关API ====================

    /**
     * 通过授权码获取账户信息
     *
     * @param authCode 授权码
     * @return WMS API响应
     */
    @GetExchange(ApiPaths.GET_ACCOUNT_INFO_BY_AUTH_CODE)
    Mono<WmsApiResponse<WmsAccountInfoRes>> getAccountInfoByAuthCode(@RequestParam("Code") String authCode);

    /**
     * 通过客户码获取账户信息
     *
     * @param customerCode 客户码
     * @return WMS API响应
     */
    @GetExchange(ApiPaths.GET_ACCOUNT_INFO_BY_CUS_CODE)
    Mono<WmsApiResponse<WmsAccountInfoRes>> getAccountInfoByCustomerCode(@RequestParam("cusCode") String customerCode);

    /**
     * 采购商品扣减账户余额
     *
     * @param request 扣减请求
     * @return WMS API响应
     */
    @PostExchange(ApiPaths.DEDUCT_ACCOUNT_INFO_BY_PURCHASE)
    Mono<WmsApiResponse<DeductAccountRes>> deductAccountInfoByPurchase(@RequestBody DeductAccountReq request);

    // ==================== 采购订单相关API ====================

    /**
     * 创建采购订单
     *
     * @param request 创建订单请求
     * @return WMS API响应
     */
    @Deprecated
    @PostExchange(ApiPaths.PURCHASE_ORDER_CREATE)
    Mono<WmsApiResponse<WmsPurchaseDataRes>> createPurchaseOrder(@RequestBody WmsCreateOrderReq request);

    /**
     * 创建采购订单
     *
     * @param request 创建订单请求
     * @return WMS API响应
     */
    Mono<WmsApiResponse<List<WmsCreatePurchaseOrderRes>>> createPurchaseOrderNew(@RequestBody WmsCreateOrderReq request);

    /**
     * 查询采购订单（POST方式）
     *
     * @param request 订单查询请求
     * @return WMS API响应
     */
    @PostExchange(ApiPaths.PURCHASE_ORDER_QUERY_POST)
    Mono<WmsApiResponse<WmsPageDTO<WmsPurchaseOrderInfoRes>>> queryOrder(@RequestBody WmsOrderQueryReq request);

    /**
     * 更新采购订单, 可以批量更新
     *
     * @param request 更新订单请求
     * @return WMS API响应
     */
    @PostExchange(ApiPaths.PURCHASE_ORDER_UPDATE)
    Mono<WmsApiResponse<Void>> updatePurchaseOrderDetails(@RequestBody List<WmsPurchaseOrderDetailReq> request);

    /**
     * 查询采购订单详情（GET方式）
     *
     * @param purchaseNo    采购单号，支持多个值（逗号分隔）
     * @param orderId       1688订单号，支持多个值（逗号分隔）
     * @param wmsPurchaseNo WMS采购单号，支持多个值（逗号分隔）
     * @return WMS API响应
     */
    @GetExchange(ApiPaths.PURCHASE_ORDER_QUERY_GET)
    Mono<WmsApiResponse<List<WmsPurchaseOrderDetailsRes>>> queryOrderDetails(
        @RequestParam(value = "purchaseNo", required = false) String purchaseNo,
        @RequestParam(value = "orderID", required = false) String orderId,
        @RequestParam(value = "wmsPurchaseNo", required = false) String wmsPurchaseNo);

    // ==================== 采购入库相关API ====================

    /**
     * 入库单创建
     *
     * @param request 入库单创建请求
     * @return WMS API响应
     */
    @PostExchange(ApiPaths.INVENTORY_IN_CREATE)
    Mono<WmsApiResponse<Object>> createInventoryIn(@RequestBody WmsCreateInboundOrderReq request);

    /**
     * 入库单查询 // FIXME: 2025/7/26 请完成
     *
     * @param asnNo 入库单号
     * @return WMS API响应
     */
    @GetExchange(ApiPaths.INVENTORY_IN_QUERY)
    Mono<WmsApiResponse<Object>> queryInventoryIn(@RequestParam("asnNo") String asnNo);

    /**
     * 修改入库单 // FIXME: 2025/7/26 请完成
     *
     * @param asnNo 入库单号
     * @return WMS API响应
     */
    @GetExchange(ApiPaths.INVENTORY_IN_UPDATE)
    Mono<WmsApiResponse<Object>> updateInventoryIn(@RequestParam("asnNo") String asnNo);

    // ==================== 产品管理相关API ====================

    /**
     * 创建产品
     *
     * @param request 产品创建请求
     * @return WMS API响应
     */
    @PostExchange(ApiPaths.CREATE_PRODUCT)
    Mono<WmsApiResponse<Void>> createProduct(@RequestBody WmsCreateGoodsReq request);

    /**
     * 获取产品列表
     *
     * @param wmsProductQueryReq 产品查询请求
     * @return WMS API响应
     */
    @PostExchange(ApiPaths.GET_PRODUCT_LIST)
    WmsApiResponse<List<WmsProduct>> getProductList(@RequestBody WmsProductQueryReq wmsProductQueryReq);

    /**
     * 获取产品信息
     *
     * @param cusCode 客户码
     * @param offerId 报价ID
     * @param sku     产品SKU
     * @return WMS API响应
     */
    @GetExchange(ApiPaths.GET_PRODUCT_INFO)
    Mono<WmsApiResponse<WmsProduct>> getProductInfo(@RequestParam("cusCode") String cusCode,
            @RequestParam("offerId") String offerId, @RequestParam("sku") String sku);
}
