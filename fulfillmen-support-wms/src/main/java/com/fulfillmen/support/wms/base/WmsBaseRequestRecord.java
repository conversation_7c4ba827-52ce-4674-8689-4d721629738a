/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.base;

import com.fulfillmen.support.common.base.BaseRequestRecord;
import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import java.util.Map;

/**
 * WMS 基础请求记录接口
 * 
 * <p>扩展通用的 BaseRequestRecord 接口，为 WMS API 请求提供特定的功能：
 * <ul>
 *   <li>WMS 特有的参数校验规则</li>
 *   <li>WMS 请求参数的标准化转换</li>
 *   <li>支持客户码、API密钥等WMS特有参数</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
public interface WmsBaseRequestRecord extends BaseRequestRecord {

    /**
     * 获取客户码
     * 
     * <p>WMS系统中的客户标识，大部分API都需要此参数。</p>
     *
     * @return 客户码，如果不需要则返回null
     */
    default String getCustomerCode() {
        return null;
    }

    /**
     * 获取操作类型
     * 
     * <p>用于日志记录和监控的操作类型标识。</p>
     *
     * @return 操作类型
     */
    default String getOperationType() {
        return this.getClass().getSimpleName().replace("Req", "").replace("Request", "");
    }

    /**
     * 校验客户码
     *
     * @param customerCode 客户码
     * @param fieldName    字段名称
     * @throws ApiParameterVerificationException 当客户码为空时抛出
     */
    default void validateCustomerCode(String customerCode, String fieldName) {
        assertNotBlank(customerCode, fieldName + "不能为空");
    }

    /**
     * 校验分页参数
     *
     * @param page     页码
     * @param pageSize 页大小
     * @throws ApiParameterVerificationException 当分页参数无效时抛出
     */
    default void validatePagination(Integer page, Integer pageSize) {
        if (page != null && page < 1) {
            throw new ApiParameterVerificationException("页码必须大于0");
        }
        if (pageSize != null && (pageSize < 1 || pageSize > 1000)) {
            throw new ApiParameterVerificationException("页大小必须在1-1000之间");
        }
    }

    /**
     * 构建通用的WMS请求参数
     * 
     * <p>添加WMS系统通用的参数，如客户码、时间戳等。</p>
     *
     * @param params 现有参数映射
     */
    default void addCommonWmsParams(Map<String, String> params) {
        // 添加客户码
        String customerCode = getCustomerCode();
        if (customerCode != null) {
            params.put("customerCode", customerCode);
        }
        
        // 添加请求时间戳
        params.put("requestTime", String.valueOf(System.currentTimeMillis()));
        
        // 添加操作类型
        params.put("operationType", getOperationType());
    }

    /**
     * 转换为JSON字符串的默认实现
     * 
     * <p>提供一个简单的JSON序列化实现，实际项目中建议使用Jackson等专业库。</p>
     *
     * @param obj 要序列化的对象
     * @return JSON字符串
     */
    @Override
    default String toJsonString(Object obj) {
        if (obj == null) {
            return "{}";
        }
        
        // 简单的JSON序列化实现
        // 实际项目中应该使用Jackson或其他JSON库
        Map<String, String> params = toParams();
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":\"")
                .append(entry.getValue() != null ? entry.getValue() : "").append("\"");
            first = false;
        }
        json.append("}");
        return json.toString();
    }
}
