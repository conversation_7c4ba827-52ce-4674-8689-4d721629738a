package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 商品审核状态
 * <pre>
 *     ## 审核状态枚举
 * | 值 | 状态 | 说明 |
 * |----|------|------|
 * | 0 | 未审核 | 需要审核 |
 * | 1 | 已审核 | 审核通过 |
 * </pre>
 * <AUTHOR>
 * @date 2025/7/28 22:38
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsGoodsAuditStatusEnums {
    UNAUDITED(0, "未审核"),
    AUDITED(1, "已审核")
    ;
    @JsonValue
    private final int code;
    private final String desc;

    WmsGoodsAuditStatusEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
