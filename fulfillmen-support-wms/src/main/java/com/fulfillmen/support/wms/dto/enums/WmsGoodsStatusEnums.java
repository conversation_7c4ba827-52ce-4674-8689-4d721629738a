package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 商品状态
 * <pre>
 *     ## 商品状态枚举
 * | 值 | 状态 | 说明 |
 * |----|------|------|
 * | 0 | 禁用 | 商品不可用 |
 * | 1 | 启用 | 商品可用 |
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/28 22:30
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsGoodsStatusEnums {
    //
    DISABLED(0, "禁用"),
    ENABLED(1, "启用");
    @JsonValue
    private final int code;
    private final String desc;

    WmsGoodsStatusEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
