/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.common.base.BaseRequestRecord;
import com.fulfillmen.support.common.exception.ApiParameterVerificationException;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import java.io.Serial;
import java.io.Serializable;
import java.util.Map;
import lombok.Builder;

/**
 * <AUTHOR>
 * @date 2025-07-29 17:10
 * @description: todo
 * @since 1.0.0
 */
@Builder
public record DeductAccountRecordReq(
        /*
         * 客户编码
         */
        @JsonProperty("customerCode") String cusCode,
        /*
         * 店铺订单号 - 对应 供应商订单号
         */
        @JsonProperty("orderNo") String orderNo,
        /*
         * 商品金额
         */
        @JsonProperty("productAmount") Double productAmount,
        /*
         * 运费
         */
        @JsonProperty("shippingFee") Integer shippingFee,
        /*
         * 服务费
         */
        @JsonProperty("serviceFee") Double serviceFee,
        /*
         * 总金额
         */
        @JsonProperty("totalAmount") Double totalAmount,
        /*
         * 创建用户
         */
        @JsonProperty("createUser") String createUser,
        /*
         * 备注
         */
        @JsonProperty("remark") String remark,
        /*
         * 支付类型
         */
        @JsonProperty("payType") WmsPayTypeEnum payType) implements BaseRequestRecord, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @Override
    public Map<String, String> toParams() {
        return Map.of();
    }

    @Override
    public void requireParams() throws ApiParameterVerificationException {

    }
}
