/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fulfillmen.support.wms.base.WmsBaseRequestRecord;
import java.util.HashMap;
import java.util.Map;

/**
 * WMS 账户信息查询请求记录
 * 
 * <p>用于查询WMS账户信息的请求记录，支持通过授权码或客户码查询。</p>
 *
 * @param authCode     授权码（可选）
 * @param customerCode 客户码（可选）
 * @param queryType    查询类型（BY_AUTH_CODE 或 BY_CUSTOMER_CODE）
 * 
 * <AUTHOR>
 * @created 2025-01-29
 */
public record WmsAccountInfoRequestRecord(
    String authCode,
    String customerCode,
    QueryType queryType
) implements WmsBaseRequestRecord {

    /**
     * 查询类型枚举
     */
    public enum QueryType {
        BY_AUTH_CODE,
        BY_CUSTOMER_CODE
    }

    /**
     * 通过授权码查询的构造方法
     *
     * @param authCode 授权码
     * @return 请求记录实例
     */
    public static WmsAccountInfoRequestRecord byAuthCode(String authCode) {
        return new WmsAccountInfoRequestRecord(authCode, null, QueryType.BY_AUTH_CODE);
    }

    /**
     * 通过客户码查询的构造方法
     *
     * @param customerCode 客户码
     * @return 请求记录实例
     */
    public static WmsAccountInfoRequestRecord byCustomerCode(String customerCode) {
        return new WmsAccountInfoRequestRecord(null, customerCode, QueryType.BY_CUSTOMER_CODE);
    }

    @Override
    public String getCustomerCode() {
        return customerCode;
    }

    @Override
    public String getOperationType() {
        return "GetAccountInfo";
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        
        switch (queryType) {
            case BY_AUTH_CODE:
                if (authCode != null) {
                    params.put("Code", authCode);
                }
                break;
            case BY_CUSTOMER_CODE:
                if (customerCode != null) {
                    params.put("cusCode", customerCode);
                }
                break;
        }
        
        // 添加查询类型
        params.put("queryType", queryType.name());
        
        // 添加通用WMS参数
        addCommonWmsParams(params);
        
        return params;
    }

    @Override
    public void requireParams() {
        switch (queryType) {
            case BY_AUTH_CODE:
                assertNotBlank(authCode, "授权码不能为空");
                break;
            case BY_CUSTOMER_CODE:
                validateCustomerCode(customerCode, "客户码");
                break;
            default:
                throw new IllegalArgumentException("未知的查询类型: " + queryType);
        }
    }
}
