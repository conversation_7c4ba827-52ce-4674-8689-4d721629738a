package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品信息请求
 * 
 * <AUTHOR>
 * @created 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsProductInfoReq {
    /**
     * 客户码
     */
    @JsonProperty("cuscode")
    private String cusCode;
    /**
     * 报价ID
     */
    @JsonProperty("offerid")
    private String offerId;
    /**
     * 产品SKU
     * 可选
     */
    @JsonProperty("sku")
    private String sku;
}
