/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fulfillmen.support.wms.base.WmsBaseRequestRecord;
import java.util.HashMap;
import java.util.Map;

/**
 * WMS 产品查询请求记录
 * 
 * <p>用于查询WMS产品信息的请求记录，支持分页查询和条件筛选。</p>
 *
 * @param customerCode 客户码
 * @param page         页码（从1开始）
 * @param pageSize     页大小（默认20，最大1000）
 * @param barcode      产品条码（可选）
 * @param offerId      报价ID（可选）
 * @param sku          产品SKU（可选）
 * 
 * <AUTHOR>
 * @created 2025-01-29
 */
public record WmsProductQueryRequestRecord(
    String customerCode,
    Integer page,
    Integer pageSize,
    String barcode,
    String offerId,
    String sku
) implements WmsBaseRequestRecord {

    /**
     * 默认页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 创建基本的产品查询请求
     *
     * @param customerCode 客户码
     * @param page         页码
     * @return 请求记录实例
     */
    public static WmsProductQueryRequestRecord create(String customerCode, Integer page) {
        return new WmsProductQueryRequestRecord(customerCode, page, DEFAULT_PAGE_SIZE, null, null, null);
    }

    /**
     * 创建带条码筛选的产品查询请求
     *
     * @param customerCode 客户码
     * @param page         页码
     * @param barcode      产品条码
     * @return 请求记录实例
     */
    public static WmsProductQueryRequestRecord createWithBarcode(String customerCode, Integer page, String barcode) {
        return new WmsProductQueryRequestRecord(customerCode, page, DEFAULT_PAGE_SIZE, barcode, null, null);
    }

    /**
     * 创建产品详情查询请求
     *
     * @param customerCode 客户码
     * @param offerId      报价ID
     * @param sku          产品SKU
     * @return 请求记录实例
     */
    public static WmsProductQueryRequestRecord createForDetail(String customerCode, String offerId, String sku) {
        return new WmsProductQueryRequestRecord(customerCode, null, null, null, offerId, sku);
    }

    @Override
    public String getCustomerCode() {
        return customerCode;
    }

    @Override
    public String getOperationType() {
        if (offerId != null && sku != null) {
            return "GetProductInfo";
        }
        return "GetProductList";
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        
        // 添加客户码
        if (customerCode != null) {
            params.put("cusCode", customerCode);
        }
        
        // 添加分页参数
        if (page != null) {
            params.put("page", page.toString());
        }
        if (pageSize != null) {
            params.put("pageSize", pageSize.toString());
        }
        
        // 添加筛选条件
        if (barcode != null) {
            params.put("barcode", barcode);
        }
        if (offerId != null) {
            params.put("offerId", offerId);
        }
        if (sku != null) {
            params.put("sku", sku);
        }
        
        // 添加通用WMS参数
        addCommonWmsParams(params);
        
        return params;
    }

    @Override
    public void requireParams() {
        // 校验客户码
        validateCustomerCode(customerCode, "客户码");
        
        // 校验分页参数
        if (page != null || pageSize != null) {
            validatePagination(page, pageSize);
        }
        
        // 对于产品详情查询，需要offerId和sku
        if ("GetProductInfo".equals(getOperationType())) {
            assertNotBlank(offerId, "报价ID不能为空");
            assertNotBlank(sku, "产品SKU不能为空");
        }
    }

    /**
     * 获取实际的页码（处理null值）
     *
     * @return 页码，默认为1
     */
    public int getActualPage() {
        return page != null ? page : 1;
    }

    /**
     * 获取实际的页大小（处理null值）
     *
     * @return 页大小，默认为DEFAULT_PAGE_SIZE
     */
    public int getActualPageSize() {
        return pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
    }

    /**
     * 是否为产品详情查询
     *
     * @return true表示是产品详情查询，false表示是产品列表查询
     */
    public boolean isDetailQuery() {
        return offerId != null && sku != null;
    }

    /**
     * 是否有筛选条件
     *
     * @return true表示有筛选条件
     */
    public boolean hasFilter() {
        return barcode != null || offerId != null || sku != null;
    }
}
