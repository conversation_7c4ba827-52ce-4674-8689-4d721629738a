/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.Data;

/**
 * WMS采购数据响应
 * 2025 年 07 月 29 日 09:39:31 废弃，改结构
 * <p>定义WMS系统返回的采购数据结构，包含成功和失败的订单信息。
 * 用于批量订单创建后的结果反馈。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@Deprecated
public class WmsPurchaseDataRes {

    /**
     * 订单ID信息
     */
    @JsonAlias(value = {"orderIds", "orderIds"})
    private OrderIdsInfo orderIds;

    /**
     * 订单ID信息内部类
     */
    @Data
    public static class OrderIdsInfo {

        /**
         * 成功创建的订单列表
         */
        private List<SuccessOrderInfo> successOrders;

        /**
         * 创建失败的订单列表
         */
        private List<FailedOrderInfo> failedOrders;
    }

    /**
     * 成功订单信息
     */
    @Data
    public static class SuccessOrderInfo {

        /**
         * WMS 订单号
         */
        @JsonAlias({"purchaseNo", "orderId"})
        private String purchaseNo;

        /**
         * 供应商订单 id
         */
        private String shopOrderId;

        /**
         * 接口状态
         */
        private String status;

        /**
         * 接口描述
         */
        private String desc;
    }

    /**
     * 失败订单信息
     */
    @Data
    public static class FailedOrderInfo {

        /**
         * 供应商订单id
         */
        private String shopOrderId;

        /**
         * 接口状态
         */
        private String status;

        /**
         * 接口描述
         */
        private String desc;
    }
}
