/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.executor;

import com.fulfillmen.support.common.executor.AbstractRequestExecutionContext;
import com.fulfillmen.support.wms.autoconfigure.WmsProperties;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.util.Map;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

/**
 * WMS 请求执行上下文
 * 
 * <p>为 WMS API 调用提供特定的请求执行上下文，包括：
 * <ul>
 *   <li>WMS 特有的认证头处理（X-API-Key、X-Nonce、X-Timestamp）</li>
 *   <li>统一的日志记录格式</li>
 *   <li>WMS 特定的异常包装</li>
 *   <li>请求参数的标准化处理</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@Slf4j
public class WmsRequestExecutionContext extends AbstractRequestExecutionContext {

    private final WmsProperties wmsProperties;

    /**
     * 构造函数
     *
     * @param operationName 操作名称
     * @param wmsProperties WMS配置属性
     */
    public WmsRequestExecutionContext(String operationName, WmsProperties wmsProperties) {
        super("WMS", operationName);
        this.wmsProperties = wmsProperties;
    }

    @Override
    public MultiValueMap<String, String> buildRequestParameters(Map<String, String> params, String apiPath, boolean isJson, String jsonKey) {
        MultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>();
        
        // 添加业务参数
        if (params != null) {
            if (isJson && StringUtils.hasText(jsonKey)) {
                // JSON格式请求：将所有参数序列化为JSON字符串
                String jsonData = buildJsonData(params);
                requestParams.add(jsonKey, jsonData);
            } else {
                // 表单格式请求：直接添加参数
                params.forEach(requestParams::add);
            }
        }
        
        // 添加WMS特有的认证参数
        addWmsAuthenticationParams(requestParams);
        
        return requestParams;
    }

    @Override
    public void logRequestStart(Object request, MultiValueMap<String, String> params) {
        log.info("[WMS] {} 请求开始: operation=[{}], request=[{}]", 
            getOperationName(), getOperationName(), request);
        log.debug("[WMS] {} 请求参数: params=[{}]", getOperationName(), params);
    }

    @Override
    public void logRequestSuccess(Object request, Object response) {
        log.info("[WMS] {} 请求成功: operation=[{}]", getOperationName(), getOperationName());
        log.debug("[WMS] {} 响应数据: request=[{}], response=[{}]", 
            getOperationName(), request, response);
    }

    @Override
    public void logRequestError(Object request, Throwable error) {
        if (error instanceof WmsApiException) {
            log.warn("[WMS] {} 请求失败: operation=[{}], request=[{}], error=[{}]", 
                getOperationName(), getOperationName(), request, error.getMessage());
        } else {
            log.error("[WMS] {} 请求异常: operation=[{}], request=[{}], error=[{}]", 
                getOperationName(), getOperationName(), request, error.getMessage(), error);
        }
    }

    @Override
    public RuntimeException wrapException(Throwable error, String operationName, String serviceName) {
        if (error instanceof WmsApiException) {
            return (WmsApiException) error;
        }
        return new WmsApiException(String.format("WMS %s 操作失败", operationName), error);
    }

    /**
     * 添加WMS特有的认证参数
     *
     * @param params 请求参数
     */
    private void addWmsAuthenticationParams(MultiValueMap<String, String> params) {
        // 添加API密钥（通常在请求头中，这里作为参数示例）
        if (StringUtils.hasText(wmsProperties.getApiKey())) {
            params.add("apiKey", wmsProperties.getApiKey());
        }
        
        // 添加随机数
        params.add("nonce", UUID.randomUUID().toString().replace("-", ""));
        
        // 添加时间戳
        params.add("timestamp", String.valueOf(System.currentTimeMillis()));
        
        // 预留签名参数（如果需要）
        // String signature = generateSignature(params);
        // params.add("signature", signature);
    }

    /**
     * 构建JSON数据
     *
     * @param params 参数映射
     * @return JSON字符串
     */
    private String buildJsonData(Map<String, String> params) {
        // 简单的JSON构建，实际项目中可以使用Jackson或其他JSON库
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\"");
            first = false;
        }
        json.append("}");
        return json.toString();
    }

    /**
     * 生成签名（预留方法）
     *
     * @param params 请求参数
     * @return 签名字符串
     */
    private String generateSignature(MultiValueMap<String, String> params) {
        // 这里可以实现WMS特定的签名算法
        // 例如：HMAC-SHA256、MD5等
        return "WMS_SIGNATURE_PLACEHOLDER";
    }

    /**
     * 创建WMS请求执行上下文的工厂方法
     *
     * @param operationName 操作名称
     * @param wmsProperties WMS配置属性
     * @return WMS请求执行上下文实例
     */
    public static WmsRequestExecutionContext create(String operationName, WmsProperties wmsProperties) {
        return new WmsRequestExecutionContext(operationName, wmsProperties);
    }
}
