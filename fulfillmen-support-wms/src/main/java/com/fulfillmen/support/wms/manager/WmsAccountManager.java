/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.manager;

import com.fulfillmen.support.wms.api.EnhancedWmsApiClient;
import com.fulfillmen.support.wms.api.WmsFulfillmenAPI;
import com.fulfillmen.support.wms.dto.request.WmsAccountInfoRequestRecord;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * WMS 账户管理器
 * 
 * <p>提供WMS账户相关的业务操作，集成了通用请求执行工具类的功能：
 * <ul>
 *   <li>统一的参数校验和日志记录</li>
 *   <li>标准化的异常处理</li>
 *   <li>请求追踪和监控</li>
 *   <li>与声明式HTTP接口的无缝集成</li>
 * </ul>
 * </p>
 * 
 * <p><strong>使用示例：</strong></p>
 * <pre>{@code
 * // 通过授权码查询账户信息
 * Mono<WmsAccountInfo> accountInfo = wmsAccountManager.getAccountInfoByAuthCode("AUTH123");
 * 
 * // 通过客户码查询账户信息
 * Mono<WmsAccountInfo> accountInfo = wmsAccountManager.getAccountInfoByCustomerCode("CUST456");
 * }</pre>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WmsAccountManager {

    private final EnhancedWmsApiClient enhancedWmsApiClient;
    private final WmsFulfillmenAPI wmsFulfillmenAPI;

    /**
     * 通过授权码获取账户信息
     * 
     * <p>使用增强的API客户端执行请求，自动提供：
     * <ul>
     *   <li>参数校验（授权码不能为空）</li>
     *   <li>请求日志记录</li>
     *   <li>异常处理和包装</li>
     *   <li>响应数据提取</li>
     * </ul>
     * </p>
     *
     * @param authCode 授权码
     * @return 账户信息的Mono
     */
    public Mono<WmsAccountInfo> getAccountInfoByAuthCode(String authCode) {
        log.debug("开始查询账户信息: authCode={}", authCode);
        
        // 创建请求记录
        WmsAccountInfoRequestRecord request = WmsAccountInfoRequestRecord.byAuthCode(authCode);
        
        // 使用增强的API客户端执行请求
        return enhancedWmsApiClient.executeRequest(
            request,
            enhancedWmsApiClient.adaptDeclarativeApi(wmsFulfillmenAPI::getAccountInfo)
        );
    }

    /**
     * 通过客户码获取账户信息
     *
     * @param customerCode 客户码
     * @return 账户信息的Mono
     */
    public Mono<WmsAccountInfo> getAccountInfoByCustomerCode(String customerCode) {
        log.debug("开始查询账户信息: customerCode={}", customerCode);
        
        // 创建请求记录
        WmsAccountInfoRequestRecord request = WmsAccountInfoRequestRecord.byCustomerCode(customerCode);
        
        // 使用增强的API客户端执行请求
        return enhancedWmsApiClient.executeRequest(
            request,
            enhancedWmsApiClient.adaptDeclarativeApi(wmsFulfillmenAPI::getAccountInfo)
        );
    }

    /**
     * 同步获取账户信息（通过授权码）
     * 
     * <p>对于需要同步结果的场景，提供阻塞式的调用方法。</p>
     *
     * @param authCode 授权码
     * @return 账户信息
     */
    public WmsAccountInfo getAccountInfoByAuthCodeSync(String authCode) {
        log.debug("开始同步查询账户信息: authCode={}", authCode);
        
        WmsAccountInfoRequestRecord request = WmsAccountInfoRequestRecord.byAuthCode(authCode);
        
        return enhancedWmsApiClient.executeRequestSync(
            request,
            enhancedWmsApiClient.adaptDeclarativeApi(wmsFulfillmenAPI::getAccountInfo)
        );
    }

    /**
     * 批量获取账户信息
     * 
     * <p>演示如何处理批量请求的场景。</p>
     *
     * @param authCodes 授权码列表
     * @return 账户信息列表的Mono
     */
    public Mono<java.util.List<WmsAccountInfo>> getAccountInfoBatch(java.util.List<String> authCodes) {
        log.debug("开始批量查询账户信息: authCodes={}", authCodes);
        
        if (authCodes == null || authCodes.isEmpty()) {
            return Mono.just(java.util.Collections.emptyList());
        }
        
        // 并行执行多个请求
        java.util.List<Mono<WmsAccountInfo>> requests = authCodes.stream()
            .map(this::getAccountInfoByAuthCode)
            .collect(java.util.stream.Collectors.toList());
        
        // 合并所有结果
        return Mono.zip(requests, results -> {
            java.util.List<WmsAccountInfo> accountInfos = new java.util.ArrayList<>();
            for (Object result : results) {
                if (result instanceof WmsAccountInfo) {
                    accountInfos.add((WmsAccountInfo) result);
                }
            }
            return accountInfos;
        });
    }

    /**
     * 验证账户是否有效
     * 
     * <p>通过查询账户信息来验证账户的有效性。</p>
     *
     * @param authCode 授权码
     * @return 账户是否有效的Mono
     */
    public Mono<Boolean> validateAccount(String authCode) {
        log.debug("开始验证账户有效性: authCode={}", authCode);
        
        return getAccountInfoByAuthCode(authCode)
            .map(accountInfo -> accountInfo != null && accountInfo.isActive())
            .onErrorReturn(false); // 如果查询失败，认为账户无效
    }

    /**
     * 获取账户余额
     * 
     * <p>从账户信息中提取余额信息。</p>
     *
     * @param authCode 授权码
     * @return 账户余额的Mono
     */
    public Mono<java.math.BigDecimal> getAccountBalance(String authCode) {
        log.debug("开始查询账户余额: authCode={}", authCode);
        
        return getAccountInfoByAuthCode(authCode)
            .map(WmsAccountInfo::getBalance)
            .switchIfEmpty(Mono.just(java.math.BigDecimal.ZERO));
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 记录操作日志
     *
     * @param operation 操作名称
     * @param params    操作参数
     */
    private void logOperation(String operation, Object... params) {
        log.info("WMS账户操作: operation={}, params={}", operation, params);
    }

    /**
     * 处理业务异常
     *
     * @param operation 操作名称
     * @param error     异常信息
     * @return 包装后的异常
     */
    private RuntimeException handleBusinessException(String operation, Throwable error) {
        log.error("WMS账户操作失败: operation={}, error={}", operation, error.getMessage(), error);
        return new RuntimeException(String.format("WMS账户%s操作失败", operation), error);
    }
}
