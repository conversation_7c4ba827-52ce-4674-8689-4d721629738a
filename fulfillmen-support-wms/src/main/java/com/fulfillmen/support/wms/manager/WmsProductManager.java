/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.manager;

import com.fulfillmen.support.wms.api.EnhancedWmsApiClient;
import com.fulfillmen.support.wms.api.WmsFulfillmenAPI;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryRequestRecord;
import com.fulfillmen.support.wms.dto.response.WmsProductInfo;
import com.fulfillmen.support.wms.dto.response.WmsProductListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * WMS 产品管理器
 * 
 * <p>提供WMS产品相关的业务操作，展示如何在复杂业务场景中使用增强的API客户端。</p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WmsProductManager {

    private final EnhancedWmsApiClient enhancedWmsApiClient;
    private final WmsFulfillmenAPI wmsFulfillmenAPI;

    /**
     * 分页查询产品列表
     *
     * @param customerCode 客户码
     * @param page         页码
     * @param pageSize     页大小
     * @return 产品列表响应的Mono
     */
    public Mono<WmsProductListResponse> getProductList(String customerCode, Integer page, Integer pageSize) {
        log.debug("开始查询产品列表: customerCode={}, page={}, pageSize={}", customerCode, page, pageSize);
        
        // 创建请求记录
        WmsProductQueryRequestRecord request = new WmsProductQueryRequestRecord(
            customerCode, page, pageSize, null, null, null);
        
        // 使用增强的API客户端执行请求
        return enhancedWmsApiClient.executeRequest(
            request,
            params -> wmsFulfillmenAPI.getProductList(
                params.getFirst("cusCode"),
                Integer.valueOf(params.getFirst("page")),
                Integer.valueOf(params.getFirst("pageSize"))
            )
        );
    }

    /**
     * 根据条码查询产品
     *
     * @param customerCode 客户码
     * @param barcode      产品条码
     * @return 产品列表响应的Mono
     */
    public Mono<WmsProductListResponse> getProductByBarcode(String customerCode, String barcode) {
        log.debug("开始根据条码查询产品: customerCode={}, barcode={}", customerCode, barcode);
        
        WmsProductQueryRequestRecord request = WmsProductQueryRequestRecord.createWithBarcode(
            customerCode, 1, barcode);
        
        return enhancedWmsApiClient.executeRequest(
            request,
            params -> wmsFulfillmenAPI.getProductList(
                params.getFirst("cusCode"),
                1, // 条码查询通常只需要第一页
                20
            )
        );
    }

    /**
     * 获取产品详细信息
     *
     * @param customerCode 客户码
     * @param offerId      报价ID
     * @param sku          产品SKU
     * @return 产品信息的Mono
     */
    public Mono<WmsProductInfo> getProductInfo(String customerCode, String offerId, String sku) {
        log.debug("开始查询产品详情: customerCode={}, offerId={}, sku={}", customerCode, offerId, sku);
        
        WmsProductQueryRequestRecord request = WmsProductQueryRequestRecord.createForDetail(
            customerCode, offerId, sku);
        
        return enhancedWmsApiClient.executeRequest(
            request,
            params -> wmsFulfillmenAPI.getProductInfo(
                params.getFirst("cusCode"),
                params.getFirst("offerId"),
                params.getFirst("sku")
            )
        );
    }

    /**
     * 搜索产品（支持多种搜索条件）
     *
     * @param customerCode 客户码
     * @param keyword      搜索关键词
     * @param page         页码
     * @return 产品列表响应的Mono
     */
    public Mono<WmsProductListResponse> searchProducts(String customerCode, String keyword, Integer page) {
        log.debug("开始搜索产品: customerCode={}, keyword={}, page={}", customerCode, keyword, page);
        
        // 这里可以根据关键词的特征判断是条码、SKU还是其他类型的搜索
        WmsProductQueryRequestRecord request;
        if (isBarcode(keyword)) {
            request = WmsProductQueryRequestRecord.createWithBarcode(customerCode, page, keyword);
        } else {
            // 对于其他类型的搜索，可以扩展请求记录
            request = new WmsProductQueryRequestRecord(customerCode, page, 20, null, null, keyword);
        }
        
        return enhancedWmsApiClient.executeRequest(
            request,
            params -> wmsFulfillmenAPI.getProductList(
                params.getFirst("cusCode"),
                Integer.valueOf(params.getFirst("page")),
                20
            )
        );
    }

    /**
     * 批量获取产品信息
     *
     * @param customerCode 客户码
     * @param productKeys  产品键值对列表（offerId + sku）
     * @return 产品信息列表的Mono
     */
    public Mono<java.util.List<WmsProductInfo>> getProductInfoBatch(
            String customerCode, 
            java.util.List<ProductKey> productKeys) {
        
        log.debug("开始批量查询产品信息: customerCode={}, count={}", customerCode, productKeys.size());
        
        if (productKeys == null || productKeys.isEmpty()) {
            return Mono.just(java.util.Collections.emptyList());
        }
        
        // 并行执行多个请求
        java.util.List<Mono<WmsProductInfo>> requests = productKeys.stream()
            .map(key -> getProductInfo(customerCode, key.offerId(), key.sku()))
            .collect(java.util.stream.Collectors.toList());
        
        // 合并所有结果
        return Mono.zip(requests, results -> {
            java.util.List<WmsProductInfo> productInfos = new java.util.ArrayList<>();
            for (Object result : results) {
                if (result instanceof WmsProductInfo) {
                    productInfos.add((WmsProductInfo) result);
                }
            }
            return productInfos;
        });
    }

    /**
     * 检查产品是否存在
     *
     * @param customerCode 客户码
     * @param offerId      报价ID
     * @param sku          产品SKU
     * @return 产品是否存在的Mono
     */
    public Mono<Boolean> checkProductExists(String customerCode, String offerId, String sku) {
        log.debug("开始检查产品是否存在: customerCode={}, offerId={}, sku={}", customerCode, offerId, sku);
        
        return getProductInfo(customerCode, offerId, sku)
            .map(productInfo -> productInfo != null)
            .onErrorReturn(false);
    }

    // ==================== 辅助类和方法 ====================

    /**
     * 产品键值对记录
     *
     * @param offerId 报价ID
     * @param sku     产品SKU
     */
    public record ProductKey(String offerId, String sku) {}

    /**
     * 判断是否为条码格式
     *
     * @param keyword 关键词
     * @return 是否为条码
     */
    private boolean isBarcode(String keyword) {
        // 简单的条码格式判断逻辑
        return keyword != null && keyword.matches("\\d{8,}");
    }

    /**
     * 创建产品键值对
     *
     * @param offerId 报价ID
     * @param sku     产品SKU
     * @return 产品键值对
     */
    public static ProductKey createProductKey(String offerId, String sku) {
        return new ProductKey(offerId, sku);
    }
}
