/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.common.WmsProduct;
import com.fulfillmen.support.wms.dto.request.DeductAccountReq;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryReq;
import com.fulfillmen.support.wms.dto.response.DeductAccountRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

/**
 * WmsApiClient 测试类
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@ExtendWith(MockitoExtension.class)
class WmsApiClientTest {

    @Mock
    private WmsFulfillmenAPI wmsFulfillmenAPI;

    private WmsApiClient wmsApiClient;

    @BeforeEach
    void setUp() {
        wmsApiClient = new WmsApiClient(wmsFulfillmenAPI);
    }

    @Test
    void testGetAccountInfoByCustomerCode_Success() {
        // 准备测试数据
        String customerCode = "TEST001";
        WmsAccountInfoRes expectedResponse = new WmsAccountInfoRes();
        expectedResponse.setCustomerCode(customerCode);
        expectedResponse.setCustomerName("测试客户");
        expectedResponse.setAccountMoney(BigDecimal.valueOf(1000.0));

        WmsApiResponse<WmsAccountInfoRes> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("success");
        apiResponse.setCode(0);
        apiResponse.setMessage("成功");
        apiResponse.setData(expectedResponse);

        // 模拟API调用
        when(wmsFulfillmenAPI.getAccountInfoByCustomerCode(customerCode))
            .thenReturn(Mono.just(apiResponse));

        // 执行测试
        WmsAccountInfoRes result = wmsApiClient.getAccountInfoByCustomerCode(customerCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(customerCode, result.getCustomerCode());
        assertEquals("测试客户", result.getCustomerName());
        assertEquals(BigDecimal.valueOf(1000.0), result.getAccountMoney());
    }

    @Test
    void testGetAccountInfoByCustomerCode_ApiFailure() {
        // 准备测试数据
        String customerCode = "TEST001";
        WmsApiResponse<WmsAccountInfoRes> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("false");
        apiResponse.setCode(1);
        apiResponse.setMessage("失败");

        // 模拟API调用
        when(wmsFulfillmenAPI.getAccountInfoByCustomerCode(customerCode))
            .thenReturn(Mono.just(apiResponse));

        // 执行测试并验证异常
        assertThrows(WmsApiException.class, () -> wmsApiClient.getAccountInfoByCustomerCode(customerCode));
    }

    @Test
    void testDeductAccountInfoByPurchase_Success() {
        // 准备测试数据
        DeductAccountReq request = DeductAccountReq.builder()
            .cusCode("TEST001")
            .totalAmount(100.00)
            .productAmount(80.00)
            .serviceFee(20.00)
            .build();

        DeductAccountRes expectedResponse = new DeductAccountRes();
        expectedResponse.setCusCode("TEST001");
        expectedResponse.setOrderNo("ORDER001");
        expectedResponse.setPaymentNumber("PAY001");
        expectedResponse.setBalanceAfterCharge(900.00);
        expectedResponse.setChargedAmount(100.00);
        expectedResponse.setPaymentDetailId(12345);

        WmsApiResponse<DeductAccountRes> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("success");
        apiResponse.setCode(0);
        apiResponse.setMessage("成功");
        apiResponse.setData(expectedResponse);

        // 模拟API调用
        when(wmsFulfillmenAPI.deductAccountInfoByPurchase(any(DeductAccountReq.class)))
            .thenReturn(Mono.just(apiResponse));

        // 执行测试
        DeductAccountRes result = wmsApiClient.deductAccountInfoByPurchase(request);

        // 验证结果
        assertNotNull(result);
        assertEquals("TEST001", result.getCusCode());
        assertEquals("ORDER001", result.getOrderNo());
        assertEquals("PAY001", result.getPaymentNumber());
        assertEquals(900.00, result.getBalanceAfterCharge());
        assertEquals(100.00, result.getChargedAmount());
        assertEquals(Integer.valueOf(12345), result.getPaymentDetailId());
    }

    @Test
    void testCreatePurchaseOrder_Success() {
        // 准备测试数据
        WmsCreateOrderReq request = WmsCreateOrderReq.builder()
            .customerCode("TEST001")
            .orders(new ArrayList<>())
            .build();

        WmsPurchaseDataRes expectedResponse = new WmsPurchaseDataRes();
        WmsApiResponse<WmsPurchaseDataRes> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("success");
        apiResponse.setCode(0);
        apiResponse.setMessage("成功");
        apiResponse.setData(expectedResponse);

        // 模拟API调用
        when(wmsFulfillmenAPI.createPurchaseOrder(any(WmsCreateOrderReq.class)))
            .thenReturn(Mono.just(apiResponse));

        // 执行测试
        WmsPurchaseDataRes result = wmsApiClient.createPurchaseOrder(request);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    void testQueryOrder_Success() {
        // 准备测试数据
        WmsOrderQueryReq request = WmsOrderQueryReq.builder()
            .page(1)
            .pageSize(10)
            .build();

        WmsPageDTO<WmsPurchaseOrderInfoRes> expectedResponse = WmsPageDTO.<WmsPurchaseOrderInfoRes>builder()
            .total(1)
            .pageSize(10)
            .pageIndex(1)
            .records(new ArrayList<>())
            .build();

        WmsApiResponse<WmsPageDTO<WmsPurchaseOrderInfoRes>> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("success");
        apiResponse.setCode(0);
        apiResponse.setMessage("成功");
        apiResponse.setData(expectedResponse);

        // 模拟API调用
        when(wmsFulfillmenAPI.queryOrder(any(WmsOrderQueryReq.class)))
            .thenReturn(Mono.just(apiResponse));

        // 执行测试
        WmsPageDTO<WmsPurchaseOrderInfoRes> result = wmsApiClient.queryOrder(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getPageIndex());
    }

    @Test
    void testQueryOrderDetail_Success() {
        // 准备测试数据
        PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
            .purchaseNo("PO001")
            .build();

        List<WmsPurchaseOrderDetailsRes> expectedResponse = new ArrayList<>();
        WmsApiResponse<List<WmsPurchaseOrderDetailsRes>> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("success");
        apiResponse.setCode(0);
        apiResponse.setMessage("成功");
        apiResponse.setData(expectedResponse);

        // 模拟API调用
        when(wmsFulfillmenAPI.queryOrderDetails(
            eq("PO001"),
            eq(null),
            eq(null)))
            .thenReturn(Mono.just(apiResponse));

        // 执行测试
        List<WmsPurchaseOrderDetailsRes> result = wmsApiClient.queryOrderDetail(request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testCreateProduct_Success() {
        // 准备测试数据
        WmsProduct request = WmsProduct.builder()
            .sku("TEST-SKU-001")
            .cnName("测试产品")
            .enName("Test Product")
            .build();

        WmsApiResponse<Object> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("success");
        apiResponse.setCode(0);
        apiResponse.setMessage("成功");
//        apiResponse.setData(Void);

        // 模拟API调用
//        when(wmsFulfillmenAPI.createProduct(any(WmsCreateGoodsReq.class)))
//            .thenReturn(Mono.just(Void.class));
//
//        // 执行测试
//        Object result = wmsApiClient.createProduct(request);

        // 验证结果
//        assertNotNull(result);
    }

    @Test
    void testGetProductList_Success() {
        // 准备测试数据
        Integer page = 1;
        String barcode = "123456789";
        List<WmsProduct> expectedProducts = new ArrayList<>();
        WmsApiResponse<List<WmsProduct>> expectedResponse = new WmsApiResponse<>();
        expectedResponse.setSuccess("success");
        expectedResponse.setCode(0);
        expectedResponse.setMessage("成功");
        expectedResponse.setData(expectedProducts);

        // 模拟API调用
        when(wmsFulfillmenAPI.getProductList(WmsProductQueryReq.builder().page(page).barcode(barcode).build()))
            .thenReturn(expectedResponse);

        // 执行测试
        WmsApiResponse<List<WmsProduct>> result = wmsApiClient.getProductList(page, barcode);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertEquals("成功", result.getMessage());
        assertNotNull(result.getData());
    }
}
