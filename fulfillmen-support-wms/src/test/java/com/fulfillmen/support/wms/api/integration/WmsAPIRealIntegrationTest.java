/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.wms.BaseAPITest;
import com.fulfillmen.support.wms.api.WmsApiClient;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.request.DeductAccountReq;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateGoodsReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.response.DeductAccountRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * WMS API真实集成测试
 *
 * <p>这个测试类会真实调用WMS API，请确保有正确的配置和测试数据。
 * 测试将验证动态请求头注入、API调用、响应处理、异常处理等完整功能。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
@Tag("integration")
@Tag("wms")
class WmsAPIRealIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private WmsApiClient wmsApiClient;

    @Test
    @Tag("read")
    void shouldGetAccountInfoByAuthCode() {
        // Given - 使用提供的真实授权码
        String authCode = "MTAwMDI7NjY1Njs0QTg4RTU5NkQ1MzBGRkMxNTI2MjRENTYzMzlGMEM2RA==";

        log.info("{}开始通过授权码获取账户信息集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}授权码: {}", LOG_ITEM, authCode);

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        try {
            // When - 执行真实API调用
            WmsAccountInfoRes result = wmsApiClient.getAccountInfoByAuthCode(authCode);

            // Then - 验证响应
            assertThat(result).isNotNull();
            assertThat(result.getCustomerId()).isNotNull();
            assertThat(result.getCustomerCode()).isNotNull();
            assertThat(result.getCustomerName()).isNotNull();

            // 记录详细的响应信息
            log.info("{}API调用成功{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}客户ID: {}", LOG_ITEM, result.getCustomerId());
            log.info("{}客户名称: {}", LOG_ITEM, result.getCustomerName());
            log.info("{}客户编码: {}", LOG_ITEM, result.getCustomerCode());
            log.info("{}账户余额: {}", LOG_ITEM, result.getAccountMoney());
            log.info("{}邮箱: {}", LOG_ITEM, result.getEmail());
            log.info("{}国家: {}", LOG_ITEM, result.getCountry());
            log.info("{}省份: {}", LOG_ITEM, result.getProvince());
            log.info("{}城市: {}", LOG_ITEM, result.getCity());
            log.info("{}地址: {}", LOG_ITEM, result.getAddress());
            log.info("{}手机号: {}", LOG_ITEM, result.getMobile());
            log.info("{}电话: {}", LOG_ITEM, result.getPhone());
            log.info("{}创建时间: {}", LOG_ITEM, result.getCreateTime());
            log.info("{}状态: {}", LOG_ITEM, result.getStatus());
            log.info("{}服务费率: {}", LOG_ITEM, result.getServiceFeeRate());
            log.info("{}信用额度: {}", LOG_ITEM, result.getCreditLimit());

            // 验证关键字段
            assertThat(result.getCustomerId()).isGreaterThan(0);
            assertThat(result.getCustomerCode()).isNotEmpty();
            assertThat(result.getCustomerName()).isNotEmpty();

            recordMetrics("GetAccountInfoByAuthCode", startTime, true);

        } catch (WmsApiException e) {
            log.warn("{}WMS API调用失败（可能是预期的）{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.warn("{}错误类型: {}", LOG_ITEM, e.getClass().getSimpleName());
            log.warn("{}错误消息: {}", LOG_ITEM, e.getMessage());

            // 验证异常处理是否正确
            assertThat(e.getMessage()).isNotNull();
            assertThat(e.getMessage()).contains("WMS");

            recordMetrics("GetAccountInfoByAuthCode", startTime, false);

            log.info("{}异常处理验证通过{}", LOG_SEPARATOR, LOG_SEPARATOR);
        } catch (Exception e) {
            log.error("{}意外异常{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.error("{}错误类型: {}", LOG_ITEM, e.getClass().getSimpleName());
            log.error("{}错误消息: {}", LOG_ITEM, e.getMessage());

            recordMetrics("GetAccountInfoByAuthCode", startTime, false);
            throw e;
        }

        // 打印性能指标
        logMetrics("GetAccountInfoByAuthCode");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetAccountInfoByCustomerCode() {
        // Given
        String customerCode = "10002";

        log.info("{}开始通过客户码获取账户信息集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}客户码: {}", LOG_ITEM, customerCode);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        try {
            // When
            WmsAccountInfoRes result = wmsApiClient.getAccountInfoByCustomerCode(customerCode);

            // Then
            assertThat(result).isNotNull();
            log.info("{}API调用成功: 客户名称={}", LOG_ITEM, result.getCustomerName());

            recordMetrics("GetAccountInfoByCustomerCode", startTime, true);

        } catch (WmsApiException e) {
            log.warn("{}WMS API调用失败: {}", LOG_ITEM, e.getMessage());
            recordMetrics("GetAccountInfoByCustomerCode", startTime, false);
        }

        logMetrics("GetAccountInfoByCustomerCode");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldDeductAccountInfoByPurchase() {
        // Given
        DeductAccountReq request = DeductAccountReq.builder()
            .cusCode("TEST001")
            .totalAmount(100.00)
            .productAmount(80.00)
            .serviceFee(20.00)
            .build();

        log.info("{}开始扣减账户余额集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        try {
            // When
            DeductAccountRes result = wmsApiClient.deductAccountInfoByPurchase(request);

            // Then
            assertThat(result).isNotNull();
            log.info("{}扣减结果: 客户码={}, 订单号={}, 扣减金额={}, 余额={}",
                LOG_ITEM, result.getCusCode(), result.getOrderNo(),
                result.getChargedAmount(), result.getBalanceAfterCharge());

            recordMetrics("DeductAccountInfoByPurchase", startTime, true);

        } catch (WmsApiException e) {
            log.warn("{}WMS API调用失败: {}", LOG_ITEM, e.getMessage());
            recordMetrics("DeductAccountInfoByPurchase", startTime, false);
        }

        logMetrics("DeductAccountInfoByPurchase");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldCreatePurchaseOrder() {
        // Given
        WmsCreateOrderReq request = WmsCreateOrderReq.builder()
            .customerCode("TEST001")
            .orders(new ArrayList<>())
            .build();

        log.info("{}开始创建采购订单集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        try {
            // When
            WmsPurchaseDataRes result = wmsApiClient.createPurchaseOrder(request);

            // Then
            assertThat(result).isNotNull();
            log.info("{}创建订单成功", LOG_ITEM);

            recordMetrics("CreatePurchaseOrder", startTime, true);

        } catch (WmsApiException e) {
            log.warn("{}WMS API调用失败: {}", LOG_ITEM, e.getMessage());
            recordMetrics("CreatePurchaseOrder", startTime, false);
        }

        logMetrics("CreatePurchaseOrder");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldQueryOrder() {
        // Given
        WmsOrderQueryReq request = WmsOrderQueryReq.builder()
            .page(1)
            .pageSize(10)
            .build();

        log.info("{}开始查询采购订单集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        try {
            // When
            WmsPageDTO<WmsPurchaseOrderInfoRes> result = wmsApiClient.queryOrder(request);

            // Then
            assertThat(result).isNotNull();
            log.info("{}查询结果: 总数={}, 页大小={}, 当前页={}",
                LOG_ITEM, result.getTotal(), result.getPageSize(), result.getPageIndex());
            result.getRecords().forEach(order -> {
                log.info("{}订单号: {}", LOG_ITEM, order.getPurchaseNo());
                log.info("{}创建时间: {}", LOG_ITEM, order.getCreateTime());
                log.info("{}订单状态: {}", LOG_ITEM, order.getStatus());
                log.info("{}订单金额: {}", LOG_ITEM, order.getTotalAmount());
                log.info("{}发货时间: {}", LOG_ITEM, order.getShippingTime());
                log.info("{}完成时间: {}", LOG_ITEM, order.getCompleteTime());
                log.info("{}物流单号: {}", LOG_ITEM, order.getTrackingNo());
                log.info("{}备注: {}", LOG_ITEM, order.getRemark());
                log.info("{}平台状态: {}", LOG_ITEM, order.getPlatformStatus());
                log.info("{}平台备注: {}", LOG_ITEM, order.getPlatformRemark());
                log.info("{}支付时间: {}", LOG_ITEM, order.getPaymentTime());
            });

            recordMetrics("QueryOrder", startTime, true);

        } catch (WmsApiException e) {
            log.warn("{}WMS API调用失败: {}", LOG_ITEM, e.getMessage());
            recordMetrics("QueryOrder", startTime, false);
        }

        logMetrics("QueryOrder");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldQueryOrderDetail() {
        // Given
        PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
            .wmsPurchaseNo("C120922025072413510900003")

            .build();

        log.info("{}开始查询采购订单详情集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        try {
            // When
            List<WmsPurchaseOrderDetailsRes> result = wmsApiClient.queryOrderDetail(request);

            // Then
            assertThat(result).isNotNull();
            log.info("{}查询结果: 详情数量={}", LOG_ITEM, result.size());

            recordMetrics("QueryOrderDetail", startTime, true);

        } catch (WmsApiException e) {
            log.warn("{}WMS API调用失败: {}", LOG_ITEM, e.getMessage());
            recordMetrics("QueryOrderDetail", startTime, false);
        }

        logMetrics("QueryOrderDetail");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldCreateProduct() {
        // Given
        WmsCreateGoodsReq request = WmsCreateGoodsReq.builder()
            .sku("TEST-SKU-001")
            .cnName("测试产品")
            .enName("Test Product")
            .build();

        log.info("{}开始创建产品集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        try {
            // When
            wmsApiClient.createProduct(request);

            log.info("{}创建产品成功", LOG_ITEM);

            recordMetrics("CreateProduct", startTime, true);

        } catch (WmsApiException e) {
            log.warn("{}WMS API调用失败: {}", LOG_ITEM, e.getMessage());
            recordMetrics("CreateProduct", startTime, false);
        }

        logMetrics("CreateProduct");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetProductList() {
        // Given
        Integer page = 1;
        String barcode = "123456789";

        log.info("{}开始获取产品列表集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}页码: {}, 条码: {}", LOG_ITEM, page, barcode);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        try {
            // When
            var result = wmsApiClient.getProductList(page, barcode);

            // Then
            assertThat(result).isNotNull();
            log.info("{}获取产品列表成功: 响应状态={}", LOG_ITEM, result.isSuccess());

            recordMetrics("GetProductList", startTime, true);

        } catch (WmsApiException e) {
            log.warn("{}WMS API调用失败: {}", LOG_ITEM, e.getMessage());
            recordMetrics("GetProductList", startTime, false);
        }

        logMetrics("GetProductList");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}
