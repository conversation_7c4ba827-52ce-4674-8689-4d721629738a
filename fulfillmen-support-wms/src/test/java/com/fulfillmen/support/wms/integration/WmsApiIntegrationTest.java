/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.integration;

import com.fulfillmen.support.wms.api.EnhancedWmsApiClient;
import com.fulfillmen.support.wms.dto.request.WmsAccountInfoRequestRecord;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryRequestRecord;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfo;
import com.fulfillmen.support.wms.dto.response.WmsProductListResponse;
import com.fulfillmen.support.wms.manager.WmsAccountManager;
import com.fulfillmen.support.wms.manager.WmsProductManager;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import reactor.test.StepVerifier;

/**
 * WMS API 集成测试
 * 
 * <p>测试通用请求执行工具类与WMS API的集成效果，验证：
 * <ul>
 *   <li>请求参数的正确构建和校验</li>
 *   <li>日志记录的完整性</li>
 *   <li>异常处理的正确性</li>
 *   <li>响应数据的正确提取</li>
 *   <li>Manager层的业务逻辑</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @created 2025-01-29
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("WMS API 集成测试")
class WmsApiIntegrationTest {

    @Autowired
    private EnhancedWmsApiClient enhancedWmsApiClient;

    @Autowired
    private WmsAccountManager wmsAccountManager;

    @Autowired
    private WmsProductManager wmsProductManager;

    @Test
    @DisplayName("测试账户信息查询 - 通过授权码")
    void testGetAccountInfoByAuthCode() {
        // 准备测试数据
        String authCode = "TEST_AUTH_CODE_123";
        
        // 执行测试
        StepVerifier.create(wmsAccountManager.getAccountInfoByAuthCode(authCode))
            .expectNextMatches(accountInfo -> {
                // 验证返回的账户信息
                return accountInfo != null && accountInfo.getAuthCode().equals(authCode);
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("测试账户信息查询 - 通过客户码")
    void testGetAccountInfoByCustomerCode() {
        // 准备测试数据
        String customerCode = "TEST_CUSTOMER_456";
        
        // 执行测试
        StepVerifier.create(wmsAccountManager.getAccountInfoByCustomerCode(customerCode))
            .expectNextMatches(accountInfo -> {
                // 验证返回的账户信息
                return accountInfo != null && accountInfo.getCustomerCode().equals(customerCode);
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("测试产品列表查询")
    void testGetProductList() {
        // 准备测试数据
        String customerCode = "TEST_CUSTOMER_789";
        Integer page = 1;
        Integer pageSize = 20;
        
        // 执行测试
        StepVerifier.create(wmsProductManager.getProductList(customerCode, page, pageSize))
            .expectNextMatches(response -> {
                // 验证返回的产品列表
                return response != null && 
                       response.getPage() == page &&
                       response.getPageSize() == pageSize;
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("测试参数校验 - 空授权码")
    void testParameterValidation() {
        // 准备测试数据（空授权码）
        WmsAccountInfoRequestRecord request = WmsAccountInfoRequestRecord.byAuthCode("");
        
        // 执行测试 - 应该抛出参数校验异常
        StepVerifier.create(
            enhancedWmsApiClient.executeRequest(
                request,
                enhancedWmsApiClient.adaptDeclarativeApi(code -> null)
            )
        )
        .expectError(com.fulfillmen.support.common.exception.ApiParameterVerificationException.class)
        .verify();
    }

    @Test
    @DisplayName("测试批量账户查询")
    void testBatchAccountQuery() {
        // 准备测试数据
        java.util.List<String> authCodes = java.util.Arrays.asList(
            "AUTH_001", "AUTH_002", "AUTH_003"
        );
        
        // 执行测试
        StepVerifier.create(wmsAccountManager.getAccountInfoBatch(authCodes))
            .expectNextMatches(accountInfos -> {
                // 验证批量查询结果
                return accountInfos != null && accountInfos.size() == authCodes.size();
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("测试产品搜索功能")
    void testProductSearch() {
        // 准备测试数据
        String customerCode = "SEARCH_CUSTOMER";
        String keyword = "**************"; // 模拟条码
        Integer page = 1;
        
        // 执行测试
        StepVerifier.create(wmsProductManager.searchProducts(customerCode, keyword, page))
            .expectNextMatches(response -> {
                // 验证搜索结果
                return response != null && response.getPage() == page;
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("测试账户验证功能")
    void testAccountValidation() {
        // 准备测试数据
        String validAuthCode = "VALID_AUTH_CODE";
        String invalidAuthCode = "INVALID_AUTH_CODE";
        
        // 测试有效账户
        StepVerifier.create(wmsAccountManager.validateAccount(validAuthCode))
            .expectNext(true)
            .verifyComplete();
        
        // 测试无效账户
        StepVerifier.create(wmsAccountManager.validateAccount(invalidAuthCode))
            .expectNext(false)
            .verifyComplete();
    }

    @Test
    @DisplayName("测试产品存在性检查")
    void testProductExistence() {
        // 准备测试数据
        String customerCode = "EXIST_CUSTOMER";
        String existingOfferId = "OFFER_001";
        String existingSku = "SKU_001";
        String nonExistingOfferId = "OFFER_999";
        String nonExistingSku = "SKU_999";
        
        // 测试存在的产品
        StepVerifier.create(wmsProductManager.checkProductExists(customerCode, existingOfferId, existingSku))
            .expectNext(true)
            .verifyComplete();
        
        // 测试不存在的产品
        StepVerifier.create(wmsProductManager.checkProductExists(customerCode, nonExistingOfferId, nonExistingSku))
            .expectNext(false)
            .verifyComplete();
    }

    @Test
    @DisplayName("测试同步API调用")
    void testSynchronousApiCall() {
        // 准备测试数据
        String authCode = "SYNC_AUTH_CODE";
        
        // 执行同步调用
        WmsAccountInfo accountInfo = wmsAccountManager.getAccountInfoByAuthCodeSync(authCode);
        
        // 验证结果
        assert accountInfo != null;
        assert accountInfo.getAuthCode().equals(authCode);
    }

    @Test
    @DisplayName("测试请求记录的参数转换")
    void testRequestRecordParameterConversion() {
        // 测试账户信息请求记录
        WmsAccountInfoRequestRecord accountRequest = WmsAccountInfoRequestRecord.byAuthCode("TEST_CODE");
        java.util.Map<String, String> accountParams = accountRequest.toParams();
        
        assert accountParams.containsKey("Code");
        assert accountParams.get("Code").equals("TEST_CODE");
        assert accountParams.containsKey("queryType");
        
        // 测试产品查询请求记录
        WmsProductQueryRequestRecord productRequest = WmsProductQueryRequestRecord.create("CUSTOMER_001", 2);
        java.util.Map<String, String> productParams = productRequest.toParams();
        
        assert productParams.containsKey("cusCode");
        assert productParams.get("cusCode").equals("CUSTOMER_001");
        assert productParams.containsKey("page");
        assert productParams.get("page").equals("2");
    }

    @Test
    @DisplayName("测试WMS特有参数的添加")
    void testWmsSpecificParameters() {
        // 创建请求记录
        WmsAccountInfoRequestRecord request = WmsAccountInfoRequestRecord.byCustomerCode("WMS_CUSTOMER");
        java.util.Map<String, String> params = request.toParams();
        
        // 验证WMS特有参数
        assert params.containsKey("requestTime");
        assert params.containsKey("operationType");
        assert params.get("operationType").equals("GetAccountInfo");
    }
}
