<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-dependencies</artifactId>
        <version>1.2.7-SNAPSHOT</version>
        <relativePath>./fulfillmen-dependencies</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>fulfillmen-starter-parent</artifactId>
    <name>Fulfillmen Starter Parent ${project.version}</name>
    <packaging>pom</packaging>

    <!-- 开发人员 -->
    <developers>
        <developer>
            <id>james</id>
            <name>james</name>
            <email><EMAIL></email>
            <roles>
                <role>Creator</role>
                <role>Java Development Engineer</role>
            </roles>
            <timezone>+8</timezone>
        </developer>
    </developers>


    <description>
        提供公共的服务能力
        提供 中间件的封装处理 等
        采用 spring boot starter 方式封装
    </description>
    <modules>
        <!-- 定义工程所需的依赖、插件、profiles -->
        <module>fulfillmen-dependencies</module>
        <!-- 定义内部模块版本管理，提供外部系统，按需引入服务 -->
        <module>fulfillmen-starter-bom</module>
        <module>fulfillmen-starter-core</module>
        <module>fulfillmen-starter-api-doc</module>
        <module>fulfillmen-starter-cache</module>
        <module>fulfillmen-starter-auth</module>
        <module>fulfillmen-starter-security</module>
        <module>fulfillmen-starter-web</module>
        <module>fulfillmen-starter-json</module>
        <module>fulfillmen-starter-data</module>
        <module>fulfillmen-starter-log</module>
        <module>fulfillmen-starter-captcha</module>
        <module>fulfillmen-starter-storage</module>
        <module>fulfillmen-starter-messaging</module>
    </modules>

    <properties>
        <!-- jdk version 17 -->
        <java.version>17</java.version>
        <!-- 强制覆盖外部settings.xml配置 -->
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>
        <resource.delimiter>@</resource.delimiter>
        <maven.javadoc.failOnError>false</maven.javadoc.failOnError>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <jacoco.version>0.8.11</jacoco.version>
    </properties>

    <!-- 依赖版本管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- 内部模块版本管理 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-bom</artifactId>
                <version>1.2.7-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Lombok（在 Java 开发过程中用注解的方式，简化了 JavaBean 的编写，避免了冗余和样板式代码，让编写的类更加简洁） -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>apply</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <java>
                        <removeUnusedImports/>
                        <eclipse>
                            <file>.style/p3c-codestyle.xml</file>
                        </eclipse>
                        <licenseHeader>
                            <file>.style/license-header</file>
                        </licenseHeader>
                    </java>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 定义不同的构建配置 -->
    <profiles>
        <!-- 默认profile: 跳过测试 -->
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>3.5.2</version>
                        <configuration>
                            <skipTests>true</skipTests>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- 测试profile: 执行测试和覆盖率检查 -->
        <profile>
            <id>test</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>3.5.2</version>
                        <configuration>
                            <skipTests>false</skipTests>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>${jacoco.version}</version>
                        <executions>
                            <execution>
                                <id>prepare-agent</id>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>report</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>report</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>check</id>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <rule>
                                            <element>BUNDLE</element>
                                            <limits>
                                                <limit>
                                                    <counter>LINE</counter>
                                                    <value>COVEREDRATIO</value>
                                                    <minimum>0.80</minimum>
                                                </limit>
                                                <limit>
                                                    <counter>BRANCH</counter>
                                                    <value>COVEREDRATIO</value>
                                                    <minimum>0.70</minimum>
                                                </limit>
                                            </limits>
                                        </rule>
                                    </rules>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
